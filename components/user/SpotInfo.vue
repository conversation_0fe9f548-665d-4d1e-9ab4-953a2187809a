<template>
  <div>
    <el-tabs v-model="sub_cur_tab" @tab-click="get_data(true)">
      <el-tab-pane label="现货资产" name="spot_assets" value="spot_assets">
        <div>
          总资产市值USD：{{ spot_data.balance }}
          <el-tooltip :content="'P2P T + n未到期资产(USD): ' + spot_data.t_plus_n_limit" placement="top">
            <i class="el-icon-question" style="color: #909399; cursor: pointer; margin-left: 5px;"></i>
          </el-tooltip>
        </div> 
        <el-row>
          <el-col :span="5">
            <el-table v-if="sub_cur_tab === 'spot_assets'" v-loading="loading" :data="left_info_table"
                      :show-header="false" stripe>
              <el-table-column prop="name" width="200px"></el-table-column>
              <el-table-column>
                <template slot-scope="scope">
                  {{ scope.row.value }}
                  <span v-if="scope.row.name === '隐藏小额资产'">
                  <el-switch
                    :disabled="loading"
                    v-model="margin_hide"
                    @change="get_spot_info">
                  </el-switch>
                </span>
                </template>
              </el-table-column>
            </el-table>
          </el-col>

          <el-col :span="5">
            <el-table v-if="sub_cur_tab === 'spot_assets'" v-loading="loading" :data="left_info_table_dw"
                      :show-header="false" stripe>
              <el-table-column prop="name" width="200px"></el-table-column>
              <el-table-column>
                <template slot-scope="scope">
                  {{ scope.row.value }}
                  <span v-if="scope.row.name === '隐藏充提总额'">
                  <el-switch
                    :disabled="loading"
                    v-model="hide_deposit_withdrawal"
                    @change="get_spot_info">
                  </el-switch>
                </span>
                </template>
              </el-table-column>
            </el-table>
          </el-col>

          <el-col :span="5">
            <el-table v-if="sub_cur_tab === 'spot_assets'" v-loading="loading" :data="left_info_table_offline"
                      :show-header="false" stripe>
              <el-table-column prop="name" width="200px"></el-table-column>
              <el-table-column>
                <template slot-scope="scope">
                  {{ scope.row.value }}
                  <span v-if="scope.row.name === '隐藏下架资产'">
                  <el-switch
                    :disabled="loading"
                    v-model="hide_offline_assets"
                    @change="get_spot_info">
                  </el-switch>
                </span>
                </template>
              </el-table-column>
            </el-table>
          </el-col>

        </el-row>
        <br/>
        <el-row>
          <el-form>
            <el-form-item label="币种" :model="asset">
              <el-select v-model="asset"
                         clearable
                         filterable
                         placeholder="<ALL>"
                         @change="get_spot_info"
                         style="width: 120px;">
                <el-option v-for="asset in $store.state.spot.info.asset_list"
                           :key="asset"
                           :label="asset"
                           :value="asset">
                </el-option>
              </el-select>
            </el-form-item>
          </el-form>
        </el-row>

        <UserWebAuthn
          ref="UserWebAuthn"
          :operation_type="operation_type"
        ></UserWebAuthn>

        <br/>
        <el-table style="width: 100%" v-loading="loading" v-if="sub_cur_tab === 'spot_assets'"
                  :data="spot_data.user_assets"
                  stripe>
          <el-table-column
            prop="asset"
            label="币种"
            width="80">
          </el-table-column>

          <el-table-column
            prop="market_value_usd"
            label="市值">
          </el-table-column>

          <el-table-column
            prop="total"
            label="数量">
            <template slot-scope="scope">
              <el-link
                type="primary"
                :href="`/users/user-details/?id=${user.id}&tab=balance_history_info&asset=${scope.row.asset}`"
                :underline="false"
                target="_blank">
                {{ scope.row.total }}
              </el-link>
            </template>
          </el-table-column>

          <el-table-column
            prop="available"
            label="可用">
          </el-table-column>

          <el-table-column
            width="200"
            prop="lock"
            label="冻结(锁定)">
            <template slot-scope="scope">
              <span>
                {{ scope.row.frozen }}
                (
                <el-link
                  type="primary"
                  :href="`/asset/lock/?asset=${scope.row.asset}&user_id=${user.id}`"
                  :underline="false"
                  target="_blank">
                  {{ scope.row.lock }}
                </el-link>
                )
              </span>
            </template>
          </el-table-column>

          <el-table-column
            prop="p2p_lock"
            label="p2p锁定">
          </el-table-column>

          <el-table-column
            prop="lock"
            label="充值总额">
            <template slot-scope="scope">
              <el-link
                type="primary"
                :href="`/asset/deposits/?asset=${scope.row.asset}&user=${user.id}`"
                :underline="false"
                target="_blank">
                <span v-if="hide_deposit_withdrawal"> -- </span>
                <span v-else>
                  {{ spot_data.deposit_records[scope.row.asset] ? spot_data.deposit_records[scope.row.asset] : 0 }}
                </span>

              </el-link>
            </template>
          </el-table-column>

          <el-table-column
            prop="withdraw"
            label="提现总额">
            <template slot-scope="scope">
              <el-link
                type="primary"
                :href="`/asset/withdrawals/?asset=${scope.row.asset}&user=${user.id}`"
                :underline="false"
                target="_blank">
                <span v-if="hide_deposit_withdrawal"> -- </span>
                <span v-else>
                 {{ spot_data.withdraw_records[scope.row.asset] ? spot_data.withdraw_records[scope.row.asset] : 0 }}
                </span>
              </el-link>

            </template>
          </el-table-column>

          <el-table-column
            width="300"
            prop="opeator"
            label="资产操作">
            <template slot-scope="scope">
              <el-row>
                <el-button size="mini" type="success" @click="handle_update_balance(scope.row.asset)">修改</el-button>
                <el-button size="mini" type="danger" @click="handle_locked_balance(scope.row.asset)">锁定</el-button>
                <el-button size="mini" type="primary" @click="handle_unlocked_balance(scope.row.asset)">解锁</el-button>
                <el-button size="small" @click="show_graph('SPOT', scope.row.asset)">图表</el-button>
              </el-row>
            </template>
          </el-table-column>

        </el-table>
      </el-tab-pane>
      <el-tab-pane label="链上资产" name="onchain_assets" value="onchain_assets">
        <div>总资产市值USD：{{ onchain_data.total_volume }}</div>

        <br/>

        <el-row>
          <el-form :inline="true">
            <el-form-item label="合约地址">
              <el-input v-model="onchain_data.onchain_contract" placeholder="请输入" style="width: 600px;"/>
            </el-form-item>
            <el-form-item>
              <el-button icon="el-icon-search" @click="get_onchain_info" circle></el-button>
            </el-form-item>
          </el-form>
        </el-row>

        <UserWebAuthn ref="UserWebAuthn" :operation_type="operation_type"></UserWebAuthn>

        <br/>

        <el-table style="width: 100%" v-loading="loading" v-if="sub_cur_tab === 'onchain_assets'"
                  :data="onchain_data.items" stripe>
          <el-table-column prop="symbol" label="symbol" width="100px"/>
          <el-table-column prop="name" label="name" width="200px"/>
          <el-table-column prop="chain" label="公链" width="60px"/>
          <el-table-column prop="contract" label="合约地址" width="400px"/>
          <el-table-column prop="volume" label="市值"/>
          <el-table-column prop="amount" label="数量"/>
          <el-table-column prop="available" label="可用"/>
          <el-table-column prop="frozen" label="冻结"/>
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="充值记录" v-loading="loading" name="spot_deposits" value="spot_deposits">
        <template v-if="sub_cur_tab === 'spot_deposits'">
          <el-form :inline="true">
            <el-form-item label="币种" :model="search_data">
              <el-select v-model="search_data.asset"
                        clearable
                        filterable
                        placeholder="<ALL>"
                        @change="handle_asset_selection"
                        style="width: 120px;">
                <el-option v-for="asset in asset_list"
                          :key="asset"
                          :label="asset"
                          :value="asset">
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="链" :model="search_data">
              <el-select v-model="search_data.chain"
                        clearable
                        filterable
                        placeholder="<ALL>"
                        @change="handle_chain_selection"
                        style="width: 120px;">
                <el-option v-for="chain in chain_list"
                          :key="chain"
                          :label="chain"
                          :value="chain">
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="类型" :model="search_data">
              <el-select v-model="search_data.type"
                        clearable
                        placeholder="<ALL>"
                        @change="handle_type_selection"
                        style="width: 120px;">
                <el-option v-for="(name, type) in types"
                          :key="type"
                          :label="name"
                          :value="type">
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item prop="start" label="时间">
              <el-date-picker
                @change="handle_date_range_selection"
                v-model="filters_mid.date_range[0]"
                type="datetime"
                value-format="timestamp"
                placeholder="开始时间">
              </el-date-picker>
            </el-form-item>
            <el-form-item prop="end" label="">
              <el-date-picker
                @change="handle_date_range_selection"
                v-model="filters_mid.date_range[1]"
                type="datetime"
                value-format="timestamp"
                placeholder="结束时间">
              </el-date-picker>
            </el-form-item>

            <el-form-item>
              <el-tooltip content="刷新" placement="right" :open-delay="500" :hide-after="2000">
                <el-button icon="el-icon-refresh-left"
                          circle
                          @click="handle_page_refresh"></el-button>
              </el-tooltip>
            </el-form-item>

          </el-form>
          <div style="margin-top: 5px; margin-bottom: 15px;">充值币种折算USD总和：{{ cur_deposit_total_usd }}</div>
        </template>
        <el-table style="width: 100%" v-loading="loading" v-if="sub_cur_tab === 'spot_deposits'" :data="items" stripe>
          <el-table-column label="ID"
                       prop="id"
                       align="right"
                       width="80px"
                       show-overflow-tooltip>
          </el-table-column>

          <el-table-column label="钱包"
                          prop="wallet_deposit_id"
                          align="right"
                          width="100px"
                          show-overflow-tooltip>
            <template slot-scope="scope">
            <span slot="reference">
                  <el-link
                          :href="$walletAdminUrl + '/deposits/records?' + jump_wallet(scope.row.wallet_deposit_id)"
                          type="primary"
                          :underline="false"
                          target="_blank"
                          style="width: 100%; font-weight: normal; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                      {{ scope.row.wallet_deposit_id}}
                  </el-link>
                </span>
            </template>
          </el-table-column>

          <el-table-column label="创建时间"
                          :formatter="row => format_date(row.created_at)"
                          show-overflow-tooltip>
          </el-table-column>
          <el-table-column label="入账时间"
                          :formatter="row => row.confirmed_at?format_date(row.confirmed_at):''"
                          show-overflow-tooltip>
          </el-table-column>

          <el-table-column label="类型"
                          :formatter="row => types[row.type]"
                          show-overflow-tooltip>
          </el-table-column>

          <el-table-column label="币种"
                          show-overflow-tooltip>
            <template slot-scope="scope">
                <span slot="reference">
                  <el-link v-on:click="search_data.asset = scope.row.asset; handle_asset_selection(scope.row.chain)"
                          :underline="false">
                    {{ scope.row.asset }}
                  </el-link>
                </span>
            </template>
          </el-table-column>

          <el-table-column label="链"
                          show-overflow-tooltip>
            <template slot-scope="scope">
              <el-link v-on:click="search_data.chain = scope.row.chain; handle_chain_selection()"
                      :underline="false">
                {{ scope.row.chain }}
              </el-link>
            </template>
          </el-table-column>

          <el-table-column label="数量"
                          align="right"
                          show-overflow-tooltip>
            <template slot-scope="scope">
              <PrettyAmount :amount="scope.row.amount" with_separator decimals="8" shrink_fraction/>
            </template>
          </el-table-column>

          <el-table-column label="地址/站内转账发送方"
                          width="170px">
            <template slot-scope="scope">
              <span v-if="scope.row.type !== 'LOCAL'">
                <el-popover v-if="scope.row.address"
                            placement="left"
                            width="240"
                            trigger="hover">
                  <el-row type="flex" justify="space-around" align="middle">
                    <el-col :span="21">
                      {{ scope.row.address }}
                    </el-col>
                    <el-col :span="2">
                      <el-tooltip content="复制" placement="top" :open-delay="500" :hide-after="2000">
                        <el-button icon="el-icon-document-copy"
                                  size="mini"
                                  circle
                                  v-clipboard:copy="scope.row.address"
                                  v-clipboard:success="handle_content_copy"></el-button>
                      </el-tooltip>
                    </el-col>
                  </el-row>

                  <span slot="reference">
                    <el-link :href="scope.row.address_url"
                            type="primary"
                            target="_blank"
                            :underline="false"
                            style="width: 100%; font-weight: normal; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                      <Truncated :text="scope.row.address" head="8" tail="6"/>
                    </el-link>
                  </span>
                </el-popover>
              </span>

              <span v-else>
                <el-link :href="'/users/user-details?id=' + scope.row.sender_user_id"
                        type="primary"
                        target="_blank"
                        :underline="false"
                        style="width: 100%; font-weight: normal; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                  {{ scope.row.sender_user_id }}
                </el-link>
              </span>
            </template>
          </el-table-column>

          <el-table-column label="Memo"
                          prop="memo"
                          width="140px"
                          show-overflow-tooltip>
          </el-table-column>

          <el-table-column label="交易 ID"
                          width="180px">
            <template slot-scope="scope">
              <el-popover v-if="scope.row.tx_id"
                          placement="right"
                          width="240"
                          trigger="hover">
                <el-row type="flex" justify="space-around" align="middle">
                  <el-col :span="21">
                    {{ scope.row.tx_id }}
                  </el-col>
                  <el-col :span="2">
                    <el-tooltip content="复制" placement="top" :open-delay="500" :hide-after="2000">
                      <el-button icon="el-icon-document-copy"
                                size="mini"
                                circle
                                v-clipboard:copy="scope.row.tx_id"
                                v-clipboard:success="handle_content_copy"></el-button>
                    </el-tooltip>
                  </el-col>
                </el-row>

                <span slot="reference">
                  <el-link :href="scope.row.tx_url"
                          type="primary"
                          target="_blank"
                          :underline="false"
                          style="width: 100%; font-weight: normal; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                    <Truncated :text="scope.row.tx_id" head="10" tail="6"/>
                  </el-link>
                </span>
              </el-popover>
            </template>
          </el-table-column>

          <el-table-column label="确认数"
                          prop="confirmations"
                          align="right"
                          show-overflow-tooltip>
          </el-table-column>

          <el-table-column label="状态"
                          show-overflow-tooltip>
            <template slot-scope="scope">
              <span :style="scope.row.status === 'TO_HOT' && !scope.row.to_hot_history_id? {color: 'orange'} : null">
                {{ statuses[scope.row.status] }}
              </span>
            </template>
          </el-table-column>

        </el-table>
        <el-pagination :current-page.sync="search_data.page"
                        :page-size.sync="search_data.limit"
                        @size-change="handle_limit_change"
                        @current-change="handle_page_change"
                        :page-sizes="[50]"
                        :total="total"
                        layout="sizes, prev, pager, next">
        </el-pagination>
      </el-tab-pane>
      <el-tab-pane label="提现记录" name="spot_withdrawals" value="spot_withdrawals">
        <template v-if="sub_cur_tab === 'spot_withdrawals'">
          <el-form :inline="true">
            <el-form-item label="币种">
              <el-select v-model="search_data.asset"
                        clearable
                        filterable
                        placeholder="<ALL>"
                        @change="handle_asset_selection"
                        style="width: 120px;">
                <el-option v-for="asset in asset_list"
                          :key="asset"
                          :label="asset"
                          :value="asset">
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="链">
              <el-select v-model="search_data.chain"
                        clearable
                        filterable
                        placeholder="<ALL>"
                        @change="handle_chain_selection"
                        style="width: 120px;">
                <el-option v-for="chain in chain_list"
                          :key="chain"
                          :label="chain"
                          :value="chain">
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="类型">
              <el-select v-model="search_data.type"
                        clearable
                        placeholder="<ALL>"
                        @change="handle_type_selection"
                        style="width: 120px;">
                <el-option v-for="(name, type) in types"
                          :key="type"
                          :label="name"
                          :value="type">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item prop="start" label="时间">
              <el-date-picker
                @change="handle_date_range_selection"
                v-model="filters_mid.date_range[0]"
                type="datetime"
                value-format="timestamp"
                placeholder="开始时间">
              </el-date-picker>
            </el-form-item>
            <el-form-item prop="end" label="">
              <el-date-picker
                @change="handle_date_range_selection"
                v-model="filters_mid.date_range[1]"
                type="datetime"
                value-format="timestamp"
                placeholder="结束时间">
              </el-date-picker>
            </el-form-item>

            <el-form-item>
              <el-tooltip content="刷新" placement="right" :open-delay="500" :hide-after="2000">
                <el-button icon="el-icon-refresh-left"
                          circle
                          @click="handle_page_refresh"></el-button>
              </el-tooltip>
            </el-form-item>
          </el-form>
        </template>
        <el-table style="width: 100%" v-loading="loading" v-if="sub_cur_tab === 'spot_withdrawals'" :data="items" stripe>
          <el-table-column label="ID"
                       prop="id"
                       align="right"
                       show-overflow-tooltip>
          </el-table-column>

          <el-table-column label="创建时间"
                          show-overflow-tooltip>
            <template slot-scope="scope">
              <el-popover placement="left" width="220" trigger="hover" :open-delay="100">
                <div><b>确认时间:</b> {{ format_date(scope.row.approved_by_user_at)}} </div>
                <div><b>打出时间:</b> {{ format_date(scope.row.sent_at)}}</div>
                <span slot="reference">
                  {{ format_date(scope.row.created_at)}}
                </span>
              </el-popover>
            </template>
          </el-table-column>

          <el-table-column label="类型"
                          :formatter="row => types[row.type]"
                          show-overflow-tooltip>
          </el-table-column>

          <el-table-column label="币种"
                          show-overflow-tooltip>
            <template slot-scope="scope">
                <span slot="reference">
                  <el-link v-on:click="search_data.asset = scope.row.asset; handle_asset_selection()"
                          :underline="false">
                    {{ scope.row.asset }}
                  </el-link>
                </span>
            </template>
          </el-table-column>

          <el-table-column label="链"
                          show-overflow-tooltip>
            <template slot-scope="scope">
              <el-link v-on:click="search_data.chain = scope.row.chain; handle_chain_selection()"
                      :underline="false">
                {{ scope.row.chain }}
              </el-link>
            </template>
          </el-table-column>

          <el-table-column label="数量"
                          prop="amount"
                          align="right"
                          show-overflow-tooltip>
            <template slot-scope="scope">
              <el-popover  placement="left" width="200" trigger="hover" :open-delay="100">
                <div><b>市值:</b> <PrettyAmount :amount="amount_usd" precision="3" with_separator shrink_fraction/> USD</div>
                <span slot="reference">
                  <PrettyAmount :amount="scope.row.amount" with_separator decimals="8" shrink_fraction/>
                </span>
              </el-popover>
            </template>
          </el-table-column>

          <el-table-column label="地址"
                          width="115px">
            <template slot-scope="scope">
              <el-popover v-if="scope.row.address"
                          placement="left"
                          width="240"
                          trigger="hover">
                <el-row type="flex" justify="space-around" align="middle">
                  <el-col :span="21">
                    {{ scope.row.address }}
                  </el-col>
                  <el-col :span="2">
                    <el-tooltip content="复制" placement="top" :open-delay="500" :hide-after="2000">
                      <el-button icon="el-icon-document-copy"
                                size="mini"
                                circle
                                v-clipboard:copy="scope.row.address"
                                v-clipboard:success="handle_content_copy"></el-button>
                    </el-tooltip>
                  </el-col>
                </el-row>

                <span slot="reference">
                  <el-link v-if="scope.row.type !== 'LOCAL'"
                          :href="addresses[scope.$index]"
                          type="primary"
                          target="_blank"
                          :underline="false"
                          style="width: 100%; font-weight: normal; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                    <Truncated :text="scope.row.address" head="5" tail="4"/>
                  </el-link>

                  <el-link v-else
                          :href="'/users/user-details?id=' + scope.row.recipient_user_id"
                          type="primary"
                          target="_blank"
                          :underline="false"
                          style="width: 100%; font-weight: normal; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                    {{ scope.row.address }}
                  </el-link>
                </span>
              </el-popover>
            </template>
          </el-table-column>

          <el-table-column label="Memo"
                          prop="memo"
                          width="80px"
                          show-overflow-tooltip>
          </el-table-column>

          <el-table-column label="交易ID"
                          width="120px">
            <template slot-scope="scope">
              <el-popover v-if="scope.row.tx_id"
                          placement="left"
                          width="240"
                          trigger="hover">
                <el-row type="flex" justify="space-around" align="middle">
                  <el-col :span="21">
                    {{ scope.row.tx_id }}
                  </el-col>
                  <el-col :span="2">
                    <el-tooltip content="复制" placement="top" :open-delay="500" :hide-after="2000">
                      <el-button icon="el-icon-document-copy"
                                size="mini"
                                circle
                                v-clipboard:copy="scope.row.tx_id"
                                v-clipboard:success="handle_content_copy"></el-button>
                    </el-tooltip>
                  </el-col>
                </el-row>

                <span slot="reference">
                  <el-link :href="txs[scope.$index]"
                          type="primary"
                          target="_blank"
                          :underline="false"
                          style="width: 100%; font-weight: normal; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                    <Truncated :text="scope.row.tx_id" head="5" tail="4"/>
                  </el-link>
                </span>
              </el-popover>
            </template>
          </el-table-column>

          <el-table-column label="手续费"
                          prop="fee"
                          align="right"
                          show-overflow-tooltip>
            <template slot-scope="scope">
              {{ scope.row.fee }} {{ scope.row.fee_asset }}
            </template>
          </el-table-column>

          <el-table-column label="确认数"
                          prop="confirmations"
                          align="right"
                          show-overflow-tooltip>
          </el-table-column>

          <el-table-column label="状态"
                          :formatter="row => statuses[row.status]"
                          show-overflow-tooltip>
          </el-table-column>
        </el-table>
        <el-pagination :current-page.sync="search_data.page"
                        :page-size.sync="search_data.limit"
                        @size-change="handle_limit_change"
                        @current-change="handle_page_change"
                        :page-sizes="[50]"
                        :total="total"
                        layout="sizes, prev, pager, next">
        </el-pagination>
      </el-tab-pane>
      <el-tab-pane label="划转记录" name="spot_balance_history" value="spot_balance_history">
        <template v-if="sub_cur_tab === 'spot_balance_history'">
          <el-form :inline="true">
            <el-form-item label="币种">
              <el-select v-model="search_data.asset"
                        clearable
                        filterable
                        placeholder="<ALL>"
                        @change="handle_single_asset_selection"
                        style="width: 120px;">
                <el-option v-for="asset in coin_list"
                          :key="asset"
                          :label="asset"
                          :value="asset">
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="账户">
              <el-select v-model="search_data.account_type"
                        clearable
                        placeholder="<ALL>"
                        @change="handle_type_selection"
                        style="width: 120px;">
                <el-option v-for="(name, type) in account_type_dict"
                          :key="type"
                          :label="name"
                          :value="type">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item prop="start" label="时间">
              <el-date-picker
                @change="handle_date_range_selection"
                v-model="filters_mid.date_range[0]"
                type="datetime"
                value-format="timestamp"
                placeholder="开始时间">
              </el-date-picker>
            </el-form-item>
            <el-form-item prop="end" label="">
              <el-date-picker
                @change="handle_date_range_selection"
                v-model="filters_mid.date_range[1]"
                type="datetime"
                value-format="timestamp"
                placeholder="结束时间">
              </el-date-picker>
            </el-form-item>

            <el-form-item>
              <el-tooltip content="刷新" placement="right" :open-delay="500" :hide-after="2000">
                <el-button icon="el-icon-refresh-left"
                          circle
                          @click="handle_page_refresh"></el-button>
              </el-tooltip>
            </el-form-item>
          </el-form>
        </template>

        <el-table style="width: 100%" v-loading="loading" v-if="sub_cur_tab === 'spot_balance_history'" :data="items" stripe>

          <el-table-column label="时间"
                       :formatter="row => format_date(row.time)"
                       show-overflow-tooltip>
          </el-table-column>

          <el-table-column label="资产"
                          prop="asset"
                          show-overflow-tooltip>
          </el-table-column>

          <el-table-column label="数量"
                          prop="amount"
                          show-overflow-tooltip>
          </el-table-column>

          <el-table-column label="转出"
                          prop="from"
                          show-overflow-tooltip>
          </el-table-column>

          <el-table-column label="转入"
                          prop="to"
                          show-overflow-tooltip>
          </el-table-column>
        </el-table>

        <el-pagination :current-page.sync="search_data.page"
                        :page-size.sync="search_data.limit"
                        @size-change="handle_limit_change"
                        @current-change="handle_page_change"
                        :page-sizes="[50]"
                        :total="total"
                        layout="sizes, prev, pager, next">
        </el-pagination>
      </el-tab-pane>
    </el-tabs>

    <el-backtop></el-backtop>

    <el-dialog
      :title="lock_title"
      :visible.sync="lock_show"
      width="30%"
    >

      <el-form :model="lock_data" ref="lock_data">
        <el-form-item label="币种" required>
          <el-input disabled v-model="lock_data.asset"></el-input>
        </el-form-item>
        <el-form-item label="数量" required>
          <el-input v-model="lock_data.amount"></el-input>
        </el-form-item>
        <el-form-item label="备注" required>
          <el-input v-model="lock_data.remark"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="lock_show = false">取 消</el-button>
        <el-button v-if="lock_data.lock_type === LOCK_TYPE_LOCKED" type="primary" @click="handleWebAuthn(onSubmitLock)">确 定</el-button>
        <el-button v-else type="primary" @click="onSubmitLock">确 定</el-button>
      </span>
    </el-dialog>

    <el-dialog
      title="资产变更"
      :visible.sync="update_show"
      width="30%"
    >

      <el-form :model="update_data" ref="update_data">
        <el-form-item label="币种" required>
          <el-input disabled v-model="update_data.asset"></el-input>
        </el-form-item>
        <el-form-item label="数量" required>
          <el-input v-model="update_data.amount"></el-input>
        </el-form-item>
        <el-form-item label="变更类型" required>
          <el-select v-model="update_data.type"
                    >
              <el-option v-for="(v, k) in spot_data.asset_update_types"
                          :key="k"
                          :label="v"
                          :value="k">
              </el-option>
        </el-select>
        </el-form-item>
        <el-form-item label="备注" required>
          <el-input v-model="update_data.remark"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="update_show = false">取 消</el-button>
        <el-button type="primary" @click="handleWebAuthn(onSubmitUpdate)">确 定</el-button>
      </span>
    </el-dialog>

    <el-dialog title="持仓历史" :visible.sync="graph_data.visible" width="80%">
        <div>
        <el-form :inline="true" style="margin-left: 9%">
          <el-form-item prop="start_date" label="开始时间">
            <el-date-picker
              v-model="series_search_data.start_date"
              type="date"
              value-format="yyyy-MM-dd"
              :picker-options="pickerOptions"
              placeholder="时间"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item prop="end_date" label="结束时间">
            <el-date-picker
              v-model="series_search_data.end_date"
              type="date"
              value-format="yyyy-MM-dd"
              :picker-options="pickerOptions"
              placeholder="时间"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="do_show_graph()"
              >查询</el-button
            >
          </el-form-item>
        </el-form>
        <div style="height: 100%">
          <el-row
            type="flex"
            class="row-bg"
            justify="center"
            height="100%"
            width="100%">
            <el-col :span="20">
              <el-card>
                <highcharts
                  class="hc"
                  :reflow="true"
                  :options="graph_data.options"
                  ref="asset_user_detailed_chart"></highcharts>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {Chart} from 'highcharts-vue'
import moment from 'moment';
import UserWebAuthn from "@/components/UserWebAuthn.vue";
import PrettyAmount from "@/components/PrettyAmount.vue";
import Truncated from "@/components/Truncated.vue";
import { splat } from 'highcharts';


export default {
  name: "SpotInfo",
  props: ['user'],
  watch: {
    'user.id'() {
      this.get_data();
    }
  },
  components: {
    PrettyAmount,
    Truncated,
    UserWebAuthn,
    highcharts: Chart
  },
  mounted() {
    if (this.user) {
      this.get_data();
    }
  },
  data() {
    return {
      operation_type: "ASSET_LOCK",
      sub_cur_tab: "spot_assets",
      loading: false,
      asset: null,
      margin_hide: false,
      hide_deposit_withdrawal: true,
      hide_offline_assets: true,
      spot_data: {
        user_assets: [],
        asset_update_types: []
      },
      lock_show: false,
      lock_title: null,
      LOCK_TYPE_LOCKED: 'lock',
      LOCK_TYPE_UNLOCKED: 'unlock',
      lock_data: {
        asset: null,
        amount: null,
        lock_type: null,
        remark: null
      },
      update_show: false,
      update_data: {
        asset: null,
        amount: null,
        remark: null
      },
      series_search_data: {
          account_type: null,
          asset: null,
          start_date: null,
          end_date: null,
        },
      graph_data: {
        visible: false,
        options: null,
      },
      default_graph_options: {
          title: {
            text: '持仓历史'
          },
          xAxis: {
            type: 'datetime'
          },
          yAxis: {
            min: 0,
            title: {
              text: '持仓数量'
            }
          },
          series: [{
            name: '持仓数量',
            data: []
          }],
          tooltip: {
            formatter: function () {
              return '<b>' + this.series.name + '</b><br/>' +
                moment(Number(this.x)).format('YYYY-MM-DD')
                + ' <br/>' + this.y.toFixed(8);
            }
          }
      },
      market_value_graph_options: {
          title: {
            text: '持仓历史'
          },
          xAxis: {
            type: 'datetime'
          },
          yAxis: {
            min: 0,
            title: {
              text: '持仓市值(USD)'
            }
          },
          series: [{
            name: '持仓市值',
            data: []
          }],
          tooltip: {
            formatter: function () {
              return '<b>' + this.series.name + '</b><br/>' +
                moment(Number(this.x)).format('YYYY-MM-DD')
                + ' <br/>' + this.y.toFixed(8);
            }
          }
        },
      search_data: {
        asset: null,
        chain: null,
        type: null,
        start: null,
        end: null,
        page: null,
        limit: 50,
        account_type: '',
      },
      filters_mid: {
        date_range: [null, null],
      },
      total: 0,
      items: [],
      assets: [],
      types: {},
      statuses: {},
      asset_list: [],
      chain_list: [],
      addresses: [],
      txs: [],
      account_type_dict: {},
      onchain_data: {
        items: [],
        total_volume: 0,
        onchain_contract: '',
      },
    }
  },
  methods: {
    clear() {
      this.search_data = {
          asset: null,
          chain: null,
          type: null,
          start: null,
          end: null,
          page: null,
          limit: 50,
          account_type: '',
        }
        this.filters_mid = {
          date_range: [null, null],
        }
        this.total = 0
        this.page_count = 0
        this.items = []
        this.assets = []
        this.types = {}
        this.statuses = {}
        this.asset_list = []
        this.chain_list = []
        this.addresses = []
        this.txs = []
        this.account_type_dict = {}
    },
    get_data(reset = false) {
      if (reset === true) {
        this.clear()
      }
      if (this.sub_cur_tab === 'spot_assets') {
        this.get_spot_info.call(this)
      }
      else if (this.sub_cur_tab === 'onchain_assets') {
        this.get_onchain_info.call(this)
      }
      else if (this.sub_cur_tab === 'spot_deposits') {
        this.get_spot_deposits.call(this)
      }
      else if (this.sub_cur_tab === 'spot_withdrawals') {
        this.get_spot_withdrawals.call(this)
      }
      else if (this.sub_cur_tab === 'spot_balance_history') {
        this.get_spot_balance_history.call(this)
      }
    },
    get_spot_info() {
      if (this.sub_cur_tab === 'spot_assets') {
        let local_search_data = {}
        local_search_data.filter_small_assets = this.margin_hide
        local_search_data.hide_deposit_withdrawal = this.hide_deposit_withdrawal
        local_search_data.hide_offline_assets = this.hide_offline_assets
        local_search_data.asset = this.asset
        this.loading = true;
        this.$axios.get(
          `/api/users/${this.user.id}/spot-assets`, {params: local_search_data}
        ).then(
          res => {
            if (res.data.code === 0) {
              let filteredItems = res.data.data.user_assets;
              // debugger
              if (this.asset) {
                filteredItems = filteredItems.filter(item => item.asset === this.asset);
              }
              res.data.data.user_assets = filteredItems;
              this.spot_data = res.data.data;
              this.loading = false;
            } else {
              this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
            }
          }
        ).catch(err => {
          this.$message.error(`请求失败! (${err})`);
        });
      }
    },
    get_onchain_info() {
      if (this.sub_cur_tab === 'onchain_assets') {
        let params = {}
        if (this.onchain_data.onchain_contract !== '' && this.onchain_data.onchain_contract !== null) {
          params['contract'] = this.onchain_data.onchain_contract;
        }
        this.loading = true;
        this.$axios.get(`/api/onchain/token/${this.user.id}/balance`, {params: params}).then(
          res => {
            if (res.data.code === 0) {
              let data = res.data.data;
              this.onchain_data.items = data.items;
              this.onchain_data.total_volume = data.total_volume;
              this.loading = false;
            } else {
              this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
            }
          }
        ).catch(err => {
          this.$message.error(`请求失败! (${err})`);
        });
      }
    },
    get_spot_deposits() {
      if (this.sub_cur_tab === 'spot_deposits') {
        this.loading = true;
        if (this.filters_mid.date_range && this.filters_mid.date_range[0]){
            this.search_data['start'] =  Math.round(this.filters_mid.date_range[0]/1000).toString();
        } else {
            this.search_data['start'] = null;
        };
        if (this.filters_mid.date_range && this.filters_mid.date_range[1]){
            this.search_data['end'] =  Math.round(this.filters_mid.date_range[1]/1000).toString();
        } else {
          this.search_data['end'] = null;
        };
        this.$axios.get(`/api/users/${this.user.id}/spot-deposits`, {params: this.search_data}).then(res => {
          this.loading = false;
          if (res?.data?.code === 0) {
            let data = res.data.data;
            this.items = data.items;
            this.total = data.total;
            this.cur_deposit_total_usd = data.total_usd;
            let extra = data.extra;
            this.assets = extra.assets;
            this.types = extra.types;
            this.statuses = extra.statuses;
            this.update_asset_list();
            this.update_chain_list();
          } else {
            this.items = [];
            this.total = 0;
            this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
          }
        });
      }
    },
    jump_wallet(wallet_id){
      let params = document.location.toString().split('?')[1];
      // 充值类型映射
      var web_wallet_type_map = {
        'ABNORMAL': 'ABNORMAL',
        'ON_CHAIN': 'NORMAL',
        'LOCAL': ''
      }
      let type = this.search_data.type;
      if (type){
        var replace_type = web_wallet_type_map[type]
        if (replace_type){
          params = params.replace(type, replace_type);
        } else {
          params = params.replace('type='+type, '');
        }
      }
      params = wallet_id == -1 ? params : (params + '&id=' + wallet_id);
      return params;
    },
    get_spot_withdrawals() {
      if (this.sub_cur_tab === 'spot_withdrawals') {
        this.loading = true;
        if (this.filters_mid.date_range && this.filters_mid.date_range[0]){
            this.search_data['start'] =  Math.round(this.filters_mid.date_range[0]/1000).toString();
        } else {
            this.search_data['start'] = null;
        };
        if (this.filters_mid.date_range && this.filters_mid.date_range[1]){
            this.search_data['end'] =  Math.round(this.filters_mid.date_range[1]/1000).toString();
        } else {
          this.search_data['end'] = null;
        };
        this.$axios.get(`/api/users/${this.user.id}/spot-withdrawals`, {params: this.search_data}).then(res => {
          this.loading = false;
          if (res?.data?.code === 0) {
            let data = res.data.data;
            let count = data.items.length;
            this.txs = _.fill(Array(count), '');
            this.addresses = _.fill(Array(count), '');
            this.items = data.items;
            this.total = data.total;
            let extra = data.extra;
            this.assets = extra.assets;
            this.types = extra.types;
            this.statuses = extra.statuses;
            this.update_asset_list();
            this.update_chain_list();
            let address_body = [];
            let txs_body = [];
            _.forEach(this.items, function(item) {
              address_body.push([item.chain, item.address])
              txs_body.push([item.chain, item.tx_id])
            });
            this.get_explorer_txs_url(txs_body);
            this.get_explorer_address_url(address_body);
          } else {
            this.items = [];
            this.total = 0;
            this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
          }
        });
      }
    },
    get_explorer_address_url(body) {
      this.$axios.post('/api/asset/withdrawals/explorer-addresses-url', {addresses: body}).then(res => {
        if (res?.data?.code === 0) {
          this.addresses = res.data.data;
        } else {
          this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
        }
      });
    },
    get_explorer_txs_url(body) {
      this.$axios.post('/api/asset/withdrawals/explorer-txs-url', {txs: body}).then(res => {
        if (res?.data?.code === 0) {
          this.txs = res.data.data;
        } else {
          this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
        }
      });
    },
    get_spot_balance_history() {
      if (this.sub_cur_tab === 'spot_balance_history') {
        let local_search_data = {}
        local_search_data.asset = this.search_data.asset;
        local_search_data.account_type = this.search_data.account_type;
        local_search_data.page = this.search_data.page;
        local_search_data.limit = this.search_data.limit;
        if (this.filters_mid.date_range && this.filters_mid.date_range[0]){
          local_search_data['start_time'] =  Math.round(this.filters_mid.date_range[0]/1000).toString();
        } else {
          local_search_data['start_time'] = null;
        };
        if (this.filters_mid.date_range && this.filters_mid.date_range[1]){
          local_search_data['end_time'] =  Math.round(this.filters_mid.date_range[1]/1000).toString();
        } else {
          local_search_data['end_time'] = null;
        };
        this.loading = true;
        this.$axios.get(`/api/users/${this.user.id}/spot-balance-history`, {params: local_search_data}).then(res => {
          this.loading = false;
          if (res?.data?.code === 0) {
            let data = res.data.data;
            this.items = data.items;
            this.total = data.total;
            let extra = data.extra;
            this.coin_list = extra.coin_list;
            this.account_type_dict = extra.account_type_dict;
          } else {
            this.items = [];
            this.total = 0;
            this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
          }
        });
      }
    },
    handle_asset_selection(chain) {
      if (chain){
        this.search_data.chain = chain;
      }
      this.update_chain_list();
      this.reset_page();
      this.get_data();
    },
    handle_single_asset_selection() {
      this.reset_page();
      this.get_data();
    },
    handle_chain_selection() {
      this.reset_page();
      this.get_data();
    },
    handle_type_selection() {
      this.reset_page();
      this.get_data();
    },
    reset_page() {
      this.search_data.page = 1;
    },
    handle_date_range_selection() {
      this.reset_page();
      this.get_data();
    },
    handle_page_refresh() {
      this.reset_page();
      this.get_data();
    },
    handle_limit_change() {
      this.reset_page();
      this.get_data();
    },
    handle_page_change() {
      this.get_data();
    },

    update_asset_list() {
      this.asset_list = this.$sortAssets(Object.keys(this.assets));
    },
    update_chain_list() {
      let search_data = this.search_data;
      let chain_list;
      if (!search_data.asset) {
        let chain_to_assets = {};
        Object.values(this.assets).forEach((chains) => {
          chains.forEach((chain) => {
            chain_to_assets[chain] = (chain_to_assets[chain] || 0) + 1;
          })
        });
        chain_list = [];
        Object.keys(chain_to_assets).forEach((chain) => chain_to_assets[chain] > 1 && chain_list.push(chain));
      } else {
        chain_list = this.assets[search_data.asset];
        if (!chain_list.includes(search_data.chain)) {
          search_data.chain = null;
        }
      }
      this.chain_list = chain_list;
    },
    format_date(timestamp, pattern = 'YYYY-MM-DD HH:mm:ss') {
      return moment(Number(timestamp) * 1000).format(pattern);
    },
    handle_content_copy() {
      this.$message.success('内容已复制到剪贴板');
    },

    handle_locked_balance(asset) {
      this.lock_title = '锁定';
      this.lock_data.lock_type = this.LOCK_TYPE_LOCKED;
      this.lock_data.asset = asset;
      this.lock_data.amount = null;
      this.lock_data.remark = null;
      this.lock_show = true;
    },
    handle_unlocked_balance(asset) {
      this.lock_title = '解锁';
      this.lock_data.lock_type = this.LOCK_TYPE_UNLOCKED;
      this.lock_data.asset = asset;
      this.lock_data.amount = null;
      this.lock_data.remark = null;
      this.lock_show = true;
    },
    handle_update_balance(asset) {
      this.update_data.asset = asset;
      this.update_data.amount = null;
      this.update_data.remark = null;
      this.update_show = true;
    },
    onSubmitLock() {
        // 检查锁类型，如果不是锁定类型则不允许操作
        if (this.lock_data.lock_type !== this.LOCK_TYPE_LOCKED) {
          this.$message.error('请联系开发人员解锁');
          return;
        }
        
        this.$axios.post(
          `/api/users/${this.user.id}/asset-lock`,
          this.lock_data,
          {headers: this.headers}
        ).then(
          res => {
            if (res.data.code === 0) {
              this.lock_show = false;
              this.$message.success('成功！')
            } else {
              this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
            }
            this.get_spot_info();
          }
        ).catch(err => {
          this.$message.error(`刷新失败! (${err})`);
        });
    },
    onSubmitUpdate() {
        this.$axios.post(
          `/api/users/${this.user.id}/asset-update`,
          this.update_data,
          {headers: this.headers}
        ).then(
          res => {
            if (res.data.code === 0) {
              this.update_show = false;
              this.$message.success('成功，待复核！')
            } else {
              this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
            }
          }
        ).catch(err => {
          this.$message.error(`刷新失败! (${err})`);
        });
    },
    async handleWebAuthn(success_callback) {
      const UserWebAuthn = this.$refs["UserWebAuthn"];
      await UserWebAuthn.run();
      await UserWebAuthn.handleWebAuthn().then(() => {
        if (UserWebAuthn.success) {
          this.headers["WebAuthn-Token"] = UserWebAuthn.webauthn_token;
          if (success_callback) {
            success_callback();
          }
          return true;
        } else {
          this.$message.error("WebAuthn校验失败!");
          return false;
        }
      }).catch(err => {
        this.$message.error(`WebAuthn校验失败! ${err}`);
        return false;
      });
    },
      do_show_graph() {
        let account_type = this.series_search_data.account_type;
        let asset = this.series_search_data.asset;
        let start_date = this.series_search_data.start_date;
        let end_date = this.series_search_data.end_date;
        this.show_graph(account_type, asset, start_date, end_date);
      },
    async show_graph(account_type, asset, start_date, end_date) {
      if (account_type && asset) {
        this.graph_data.options = this.default_graph_options;
      } else {
        this.graph_data.options = this.market_value_graph_options;
      }
      let serie = this.graph_data.options.series[0];
      serie.data = [];
      this.graph_data.visible = true;
      this.series_search_data.account_type = account_type;
      this.series_search_data.asset = asset;
      let params = {account_type: account_type};
      if (start_date && end_date) {
          params['start_date'] = start_date;
          params['end_date'] = end_date;
      } else {
          this.series_search_data.start_date = null;
          this.series_search_data.end_date = null;
      }
      try {
        let res;
        if (asset) {
          res = await this.$axios.get(`/api/users/${this.user.id}/asset-series`, {params: {...params, asset: asset}})
        } else {
          res = await this.$axios.get(`/api/users/${this.user.id}/asset-sum-series`, {params: params})
        }
        if (res?.data?.code === 0) {
          serie.data = res.data.data.map(([x, y]) => [x * 1000, parseFloat(y)]);
        } else {
          this.$message.error(`获取失败! (code: ${res.data?.code}; message: ${res.data?.message})`);
        }
      } catch(e) {
        this.$message.error(`请求失败! (${err})`);
      }
    }
  },
  computed: {
    left_info_table() {
      let user_assets = this.spot_data.user_assets;
      return [
        {name: '隐藏小额资产', value: null},

      ];
    },
    left_info_table_dw() {
      return [
        {name: '隐藏充提总额', value: null},

      ];
    },
    left_info_table_offline() {
      return [
        {name: '隐藏下架资产', value: null},

      ];
    },
    headers() {
      return {
        'AUTHORIZATION': this.$cookies.get('admin_token'),
        'WebAuthn-Token': this.$refs["UserWebAuthn"] !== undefined ? this.$refs["UserWebAuthn"].webauthn_token: "",
        'Operate-Type': this.operation_type,
      }
    },
  }
}
</script>
