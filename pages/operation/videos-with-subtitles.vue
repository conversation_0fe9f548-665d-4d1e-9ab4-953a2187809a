<template>
  <div class="table-data">
    <el-row type="flex" justify="space-between" align="middle">
      <el-col :span="22">
        <h2 style="font-family: 'Microsoft YaHei',Arial,sans-serif;">
          业务视频管理
        </h2>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="22">
        <!-- 查询条件表单 -->
        <el-form :inline="true" :model="filters" size="small" style="margin-bottom: 16px;">
          <el-form-item label="视频ID">
            <el-input v-model="filters.id" placeholder="请输入视频ID" clearable style="width: 160px;" />
          </el-form-item>
          <el-form-item label="视频名称">
            <el-input v-model="filters.name" placeholder="请输入视频名称" clearable style="width: 160px;" />
          </el-form-item>
          <el-form-item label="展示位置">
            <el-select v-model="filters.business" placeholder="请选择展示位置" clearable style="width: 160px;">
              <el-option v-for="(label, value) in enums.businesses" :key="value" :label="label" :value="value" />
            </el-select>
          </el-form-item>
          <el-form-item label="操作人">
            <UserSearch v-model="filters.user_id"></UserSearch>
          </el-form-item>
          <el-form-item>
            <el-tooltip content="刷新" placement="right" :open-delay="500" :hide-after="2000">
              <el-button icon="el-icon-refresh-left"
                        circle
                        @click="handleSearch"></el-button>
            </el-tooltip>
          </el-form-item>
        </el-form>
      </el-col>
      <el-col :span="2" style="text-align: right;">
        <el-button type="primary" size="small" @click="handleCreate">上传文件</el-button>
      </el-col>
    </el-row>
    <el-table :data="items" :loading="loading" style="width: 100%" :header-cell-style="{color: 'black'}" border stripe>
      <el-table-column label="ID" prop="id" align="right" width="50" />
      <el-table-column label="视频名称" prop="name" align="left" min-width="220">
        <template slot-scope="scope">
          <el-link :href="scope.row.file_url" target="_blank" :underline="false">
            {{ scope.row.name }}
          </el-link>
          <el-tooltip content="复制链接" placement="top">
            <el-button
              type="text"
              icon="el-icon-document-copy"
              style="margin-left: 4px; padding: 0;"
              @click="copyLink(scope.row.file_url)"
            />
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="展示平台" prop="platform" align="left" min-width="70">
        <template slot-scope="scope">
          {{ enums.platforms[scope.row.platform] || scope.row.platform }}
        </template>
      </el-table-column>
      <el-table-column label="视频语言" prop="lang" align="left" min-width="60">
        <template slot-scope="scope">
          {{ enums.languages[scope.row.lang] || scope.row.lang }}
        </template>
      </el-table-column>
      <el-table-column label="字幕文件" prop="subtitles" align="left" min-width="160">
        <template slot-scope="scope">
          <el-tooltip v-if="getSubtitleDisplay(scope.row).showTooltip" placement="top" effect="dark">
            <div slot="content" v-html="getSubtitleDisplay(scope.row).tooltipContent"></div>
            <span v-html="getSubtitleDisplay(scope.row).cellContent"></span>
          </el-tooltip>
          <span v-else v-html="getSubtitleDisplay(scope.row).cellContent"></span>
        </template>
      </el-table-column>
      <el-table-column label="展示位置" prop="business" align="left" min-width="80">
        <template slot-scope="scope">
          {{ enums.businesses[scope.row.business] || scope.row.business }}
        </template>
      </el-table-column>
      <el-table-column label="最近更新时间 (UTC)" prop="update_time" align="left" min-width="100">
        <template slot-scope="scope">
          {{ scope.row.update_time }}
        </template>
      </el-table-column>
      <el-table-column label="操作人" show-overflow-tooltip prop="user_name" align="left" min-width="80">
          <template slot-scope="scope">
            <el-link
                :href="'/users/user-details?id=' + scope.row.user_id"
                type="primary"
                target="_blank"
                :underline="false"
                style="width: 100%; font-weight: normal; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;"
            >
                {{ scope.row.user_name }}
            </el-link>
          </template>
        </el-table-column>
      <el-table-column label="操作" align="center" width="220">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button 
            type="text" 
            size="small" 
            :disabled="scope.row.translating"
            :loading="scope.row.translating"
            @click="handleTranslate(scope.row)"
          >
            更新翻译
          </el-button>
          <el-button type="text" size="small" @click="showBusinessDialog(scope.row)">展示位置</el-button>
          <el-button type="text" size="small" style="color: #F56C6C;" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      style="margin-top: 16px; text-align: right;"
      :current-page.sync="filters.page"
      :page-size.sync="filters.limit"
      :total="total"
      layout="total, sizes, prev, pager, next, jumper"
      :page-sizes="[10, 20, 50, 100]"
      @size-change="handle_limit_change"
      @current-change="handle_page_change"
      :disabled="loading"
    />

    <!-- 新建/编辑视频弹窗结构 -->
    <el-dialog :visible.sync="dialogVisible" width="600px" :title="dialogTitle" :close-on-click-modal="false">
      <el-form :model="dialogForm" label-width="100px">
        <el-form-item label="视频名称" required>
          <el-input v-model="dialogForm.name" placeholder="请输入视频名称" />
        </el-form-item>
        <el-form-item label="语言" required>
          <el-select v-model="dialogForm.lang" placeholder="请选择语言">
            <el-option
              v-for="(label, value) in enums.languages"
              :key="value"
              :label="value === 'EN_US' ? label + '（视频中无语音也选英语）' : label"
              :value="value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="视频文件" required>
          <el-upload
            action
            :auto-upload="false"
            :show-file-list="true"
            :limit="1"
            :file-list="videoFileList"
            :on-change="handleVideoChange"
            accept=".mp4"
            :disabled="isEditMode"
          >
            <el-button size="small" type="primary" :loading="video_file_uploading" :disabled="isEditMode">选择文件</el-button>
          </el-upload>
        </el-form-item>
        <el-form-item label="英文字幕">
          <el-upload
            action
            :auto-upload="false"
            :show-file-list="true"
            :file-list="subtitleFileList"
            :on-change="handleSubtitleChange"
            accept=".vtt"
            ref="subtitleUpload"
          >
            <el-button size="small" type="primary" :disabled="isEditMode ? false : !dialogForm.videoFileKey">选择字幕</el-button>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="dialogLoading" @click="handleSave" :disabled="isEditMode && !dialogForm.subtitleUpdated">{{ isEditMode ? '更新' : '保存' }}</el-button>
      </div>
    </el-dialog>
    <!-- 展示位置分配弹窗 -->
    <el-dialog :visible.sync="businessDialogVisible" width="400px" title="分配展示位置" :close-on-click-modal="false">
      <el-form :model="businessDialogForm" label-width="100px">
        <el-form-item label="平台">
          <el-select v-model="businessDialogForm.platform" placeholder="请选择平台" @change="checkBusinessConflict">
            <el-option v-for="(label, value) in enums.platforms" :key="value" :label="label" :value="value" />
          </el-select>
        </el-form-item>
        <el-form-item label="展示位置">
          <el-select v-model="businessDialogForm.business" placeholder="请选择展示位置" @change="checkBusinessConflict">
            <el-option v-for="(label, value) in enums.businesses" :key="value" :label="label" :value="value" />
          </el-select>
        </el-form-item>
      </el-form>
      <template v-if="businessConflictVideo">
        <div style="color: #F56C6C; margin-bottom: 10px;">
          视频【{{businessConflictVideo.id}}-{{businessConflictVideo.name}}-{{enums.languages[businessConflictVideo.lang]}}-{{enums.platforms[businessConflictVideo.platform]}} 】
          已经被设置为在这里展示，如果点击”保存“的话，将清空原视频的展示位置。
        </div>
      </template>
      <div slot="footer" class="dialog-footer">
        <el-button @click="businessDialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="businessDialogLoading" @click="handleBusinessSave">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<style>
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
}

/* 隐藏 el-upload 组件的删除按钮 - 基于 Element UI 的实际类名 */
/* 针对所有可能的删除按钮 */
.el-upload-list [class*="close"] {
  display: none !important;
  pointer-events: none !important;
}
</style>

<script>
import SparkMD5 from "spark-md5";
import UserSearch from "@/components/user/UserSearch";

export default {
  components: {
    UserSearch,
  },
  data() {
    return {
      enums: {
        platforms: {},
        businesses: {},
        languages: {},
      },
      filters: {
        page: 1,
        limit: 20,
        id: null,
        name: '',
        business: '',
        user_id: '',
      },
      items: [],
      total: 0,
      loading: false,
      video_file_uploading: false,
      videoFileList: [], // 独立的视频 file-list
      subtitleFileList: [], // 独立的字幕 file-list
      dialogVisible: false,
      dialogTitle: '新建视频',
      isEditMode: false,
      editingVideoId: null,
      dialogForm: {
        name: '',
        business: '',
        lang: 'EN_US',
        videoFile: null,
        videoFileName: '',
        videoFileId: '',
        videoFileKey: '',
        coverFileKey: '',
        subtitleFile: null,
        subtitleFileName: '',
        subtitleFileId: '',
        subtitleFileKey: '',
        subtitleUpdated: false,
      },
      dialogLoading: false,
      businessDialogVisible: false,
      businessDialogForm: {
        id: '',
        business: '',
        platform: '',
      },
      businessDialogLoading: false,
      businessConflictVideo: null, // 新增：用于存储占用该位置的视频信息
    };
  },
  methods: {
    handleCreate() {
      this.dialogTitle = '新建视频';
      this.isEditMode = false;
      this.editingVideoId = null;
      this.dialogForm = {
        name: '',
        lang: 'EN_US',
        videoFile: null,
        videoFileName: '',
        videoFileId: '',
        videoFileKey: '',
        subtitleFile: null,
        subtitleFileName: '',
        subtitleFileId: '',
        subtitleFileKey: '',
        subtitleUpdated: false,
      };
      this.videoFileList = [];
      this.subtitleFileList = [];
      this.percentage = 0;
      this.uploadSuc = false;
      this.partList = [];
      this.requestData = {};
      this.chunkNumber = null;
      this.dialogVisible = true;
    },
    handleEdit(row) {
      this.dialogTitle = '编辑视频';
      this.isEditMode = true;
      this.editingVideoId = row.id;
      this.dialogForm = {
        name: row.name,
        lang: row.lang,
        videoFile: null,
        videoFileName: null,
        videoFileId: row.file_id,
        videoFileKey: row.file_key,
        subtitleFile: null,
        subtitleFileName: null,
        subtitleFileId: null,
        subtitleFileKey: null,
        subtitleUpdated: false,
      };
      
      // 设置视频文件列表，显示已上传的视频文件
      this.videoFileList = [{
        name: row.file_key,
        url: row.file_url,
        status: 'success'
      }];
      
      // 设置字幕文件列表，显示已上传的字幕文件
      if (row.subtitle_source && row.subtitle_source.file_key) {
        this.subtitleFileList = [{
          name: row.subtitle_source.file_key,
          url: row.subtitle_source.file_url,
          status: 'success'
        }];
      } else {
        this.subtitleFileList = [];
      }
      
      this.percentage = 0;
      this.uploadSuc = false;
      this.partList = [];
      this.requestData = {};
      this.chunkNumber = null;
      this.dialogVisible = true;
    },
    async handleDelete(row) {
      this.$confirm('确定要删除该视频吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const res = await this.$axios.delete(`/api/operation/media/video/${row.id}`);
          if (res.data && res.data.code === 0) {
            this.$message.success('删除成功');
            this.get_data();
          } else {
            this.$message.error(res.data?.message || '删除失败');
          }
        } catch (e) {
          this.$message.error('删除失败');
        }
      }).catch(() => {});
    },
    handle_limit_change(val) {
      this.filters.limit = val;
      this.filters.page = 1;
      this.get_data();
    },
    handle_page_change(val) {
      this.filters.page = val;
      this.get_data();
    },
    async get_enums() {
      try {
        const res = await this.$axios.get('/api/operation/media/video/enums');
        if (res.data && res.data.code === 0) {
          this.enums = res.data.data.enums;
        }
      } catch (e) {
        this.$message.error('获取枚举失败');
      }
    },
    async get_data() {
      this.loading = true;
      try {
        if (this.filters.id === '') {
          this.filters.id = null;
        }
        if (this.filters.user_id === '') {
          this.filters.user_id = null;
        }
        const res = await this.$axios.get('/api/operation/media/video-list', { params: this.filters });
        if (res.data && res.data.code === 0) {
          this.items = res.data.data.items;
          this.total = res.data.data.total;
        } else {
          this.items = [];
          this.total = 0;
        }
      } catch (e) {
        this.items = [];
        this.total = 0;
        this.$message.error('获取视频列表失败');
      }
      this.loading = false;
    },
    handleSearch() {
      this.filters.page = 1;
      this.get_data();
    },
    // 视频上传完全对齐 @videos.vue
    handleVideoChange(file, fileList) {
      // 只保留最新的一个文件
      this.videoFileList = fileList.slice(-1);
      if (file.status !== 'ready') return;
      file = file.raw;
      this.dialogForm.videoFile = file;
      this.dialogForm.videoFileName = file.name;
      this.dialogForm.videoFileId = '';
      this.dialogForm.videoFileKey = '';
      this.percentage = 0;
      this.abort = false;
      this.btn = false;
      this.uploadSuc = false;
      this.chunkNumber = null;
      this.handleVideoChunkUpload(file);
    },
    async handleVideoChunkUpload(file) {
      this.video_file_uploading = true;

      // 参考 @videos.vue
      const fileParse = (file, type = "base64") => {
        return new Promise(resolve => {
          let fileRead = new FileReader();
          if (type === "base64") {
            fileRead.readAsDataURL(file);
          } else if (type === "buffer") {
            fileRead.readAsArrayBuffer(file);
          }
          fileRead.onload = (ev) => {
            resolve(ev.target.result);
          };
        });
      };
      let buffer = await fileParse(file, "buffer"),
        spark = new SparkMD5.ArrayBuffer(),
        hash,
        suffix;
      spark.append(buffer);
      hash = spark.end();
      suffix = /\.([0-9a-zA-Z]+)$/i.exec(file.name)[1];
      let partList = [];
      let partsize = 5 * 1024 * 1024;
      let totalChunks = Math.ceil(file.size / partsize);
      console.log('totalChunks', totalChunks);
      console.log('partsize', partsize)
      let cur = 0;
      for(let i=1; i<= totalChunks; i++) {
        let end = cur + partsize;
        if(end > file.size) end = file.size;
        let item = {
          chunk: file.slice(cur, end),
          filename: `${hash}_${i}.${suffix}`,
          chunkNumber: `${i}`,
          file: file.slice(cur, end),
        };
        cur += partsize;
        partList.push(item);
      }
      this.requestData = {
        fileMd5: hash,
        name: file.name,
        size: file.size,
        totalChunks: totalChunks,
        chunkSize: partsize,
      };
      this.partList = partList;
      this.sendRequest();
    },
    async sendRequest() {
      this.uploadSuc = false;
      let requestList = [];
      this.partList.forEach((item, index) => {
        let fn = async (chunkNumber) => {
          let formData = new FormData(),
            shardFile = new SparkMD5.ArrayBuffer(),
            shardFileBuffer = await (new Promise(resolve => {
              let reader = new FileReader();
              reader.readAsArrayBuffer(item.chunk);
              reader.onload = (ev) => resolve(ev.target.result);
            })),
            shardFileHash;
          shardFile.append(shardFileBuffer);
          shardFileHash = shardFile.end();
          formData.append("chunkNumber", chunkNumber ? chunkNumber : item.chunkNumber);
          formData.append("file", item.file);
          formData.append("fileMd5", this.requestData.fileMd5);
          formData.append("name", this.requestData.name);
          formData.append("size", this.requestData.size);
          formData.append("totalChunks", this.requestData.totalChunks);
          formData.append("chunkSize", this.requestData.chunkSize);
          formData.append("chunkMd5", shardFileHash);
          return this.$axios.post('/api/upload/video', formData, {
            headers: { 'Content-Type': 'multipart/form-data' },
          }).then(res => {
            let code = res.data.code;
            let data = res.data.data;
            if (code == 0 && data.is_completed === false) {
              this.percentage = parseInt((data.chunkNumber / this.requestData.totalChunks) * 100);
              if (chunkNumber) {
                this.partList.splice(data.chunkNumber - 1, 1);
              }
              return data.chunkNumber;
            } else if (code == 0 && data.is_completed === true) {
              this.percentage = 100;
              this.uploadSuc = true;
              console.log('data.file_key', data.file_key);
              console.log('data.cover_key', data.cover_key);
              this.dialogForm.videoFileId = data.file_id || '';
              this.dialogForm.videoFileKey = data.file_key || '';
              this.dialogForm.coverFileKey = data.cover_key || '';
              this.$message.success('视频上传成功');
              this.video_file_uploading = false;
            } else {
              this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
            }
          }).catch(() => {
            this.$message.error('上传失败');
            this.video_file_uploading = false;
          })
        };
        requestList.push(fn);
      });
      let i = 0;
      let send = async () => {
        if (this.abort) return;
        if (i >= requestList.length && !this.uploadSuc) {
          this.$message.warning('上传失败,请重新上传');
          this.percentage = 0;
          this.video_file_uploading = false;
          return;
        }
        if (this.uploadSuc) {
          this.percentage = 100;
          this.video_file_uploading = false;
          return;
        }
        await requestList[i](this.chunkNumber).then(res => {
          this.chunkNumber = res;
        });
        i++;
        send();
      };
      send();
    },
    handleSubtitleChange(file, fileList) {
      console.log('handleSubtitleChange start', file.status, this.isEditMode);
      
      // 只保留最新的一个文件
      this.subtitleFileList = fileList.slice(-1);
      
      if (file.status !== 'ready') return;
      
      console.log('handleSubtitleChange ready');
      // 设置文件信息
      this.dialogForm.subtitleFile = file.raw;
      this.dialogForm.subtitleFileName = file.name;
      
      // 编辑模式下，需要立即上传新的字幕文件
      if (this.isEditMode) {
        // 清空之前的字幕信息，表示需要更新
        this.dialogForm.subtitleFileId = '';
        this.dialogForm.subtitleFileKey = '';
        this.dialogForm.subtitleUpdated = false;
      }
      
      // 上传字幕文件
      this.uploadSubtitleFile(file.raw);
    },
    async uploadSubtitleFile(file) {
      const formData = new FormData();
      formData.append('file', file);
      console.log('videoFileKey on uploadSubtitle', this.dialogForm.videoFileKey);
      if (!this.dialogForm.videoFileKey) {
        this.$message.error('请先上传视频文件');
        return;
      }
      formData.append('video_file_key', this.dialogForm.videoFileKey);
      formData.append('lang', 'EN_US');
      
      try {
        const res = await this.$axios.post('/api/upload/video-subtitle', formData, {
          headers: { 'Content-Type': 'multipart/form-data' },
        });
        if (res.data && res.data.code === 0) {
          const data = res.data.data;
          console.log('字幕上传返回', data);
          this.dialogForm.subtitleFileId = data.file_id || data.id;
          this.dialogForm.subtitleFileKey = data.file_key || data.key;
          this.dialogForm.subtitleUpdated = true;
          
          // 更新字幕文件列表状态
          if (this.subtitleFileList.length > 0) {
            this.subtitleFileList[0].status = 'success';
            // this.subtitleFileList[0].name = data.file_key || data.key;
          }
          
          this.$message.success('字幕上传成功');
        } else {
          this.dialogForm.subtitleFileId = '';
          this.dialogForm.subtitleFileKey = '';
          this.dialogForm.subtitleUpdated = false;
          
          // 更新字幕文件列表状态为失败
          if (this.subtitleFileList.length > 0) {
            this.subtitleFileList[0].status = 'fail';
          }
          
          this.$message.error(res.data?.message || '字幕上传失败');
        }
      } catch (e) {
        this.dialogForm.subtitleFileId = '';
        this.dialogForm.subtitleFileKey = '';
        this.dialogForm.subtitleUpdated = false;
        
        // 更新字幕文件列表状态为失败
        if (this.subtitleFileList.length > 0) {
          this.subtitleFileList[0].status = 'fail';
        }
        
        this.$message.error('字幕上传失败');
        console.error('字幕上传错误:', e);
      }
    },
    async handleSave() {
      if (!this.dialogForm.name || !this.dialogForm.lang) {
        this.$message.error('请填写完整信息');
        return;
      }
      if (!this.dialogForm.videoFileId || !this.dialogForm.videoFileKey) {
        this.$message.error('请上传视频文件');
        return;
      }
      this.dialogLoading = true;
      let subtitleParams = this.dialogForm.subtitleFileId ? {
        lang: 'EN_US',
        file_id: this.dialogForm.subtitleFileId,
        file_key: this.dialogForm.subtitleFileKey,
      } : null;

      try {
        let res;
        if (this.isEditMode) {
          if (!this.dialogForm.subtitleFileId || !this.dialogForm.subtitleFileKey) {
            this.$message.error('请上传英文字幕');
            return;
          }
          // 编辑模式：使用 PUT 方法
          const params = {
            subtitle: subtitleParams,
          };
          res = await this.$axios.put(`/api/operation/media/video/${this.editingVideoId}`, params);
        } else {
          // 新建模式：使用 POST 方法
          const params = {
            name: this.dialogForm.name,
            lang: this.dialogForm.lang,
            file_id: this.dialogForm.videoFileId,
            file_key: this.dialogForm.videoFileKey,
            cover_key: this.dialogForm.coverFileKey,
            subtitle: subtitleParams,
          };
          res = await this.$axios.post('/api/operation/media/video', params);
        }
        
        if (res.data && res.data.code === 0) {
          this.$message.success(this.isEditMode ? '更新成功' : '保存成功');
          this.dialogVisible = false;
          this.get_data();
        } else {
          this.$message.error(res.data?.message || (this.isEditMode ? '更新失败' : '保存失败'));
        }
      } catch (e) {
        this.$message.error(this.isEditMode ? '更新失败' : '保存失败');
        console.log(e);
      }
      this.dialogLoading = false;
    },
    async handleTranslate(row) {
      // 直接设置当前行的翻译状态
      this.$set(row, 'translating', true);
      
      this.$message.info('正在更新翻译...');
      try {
        const res = await this.$axios.post(`/api/operation/media/video/${row.id}/translate`);
        if (res.data && res.data.code === 0) {
          this.$message.success('已重新开始翻译');
          this.get_data();
        } else {
          this.$message.error(res.data?.message || '重新翻译失败');
        }
      } catch (e) {
        this.$message.error('重新翻译失败');
      } finally {
        // 重置翻译状态
        this.$set(row, 'translating', false);
      }
    },
    showBusinessDialog(row) {
      this.businessDialogForm = {
        id: row.id,
        business: row.business || '',
        platform: row.platform || '',
      };
      this.businessConflictVideo = null; // 打开时清空
      this.businessDialogVisible = true;
    },
    async checkBusinessConflict() {
      if (!this.businessDialogForm.business || !this.businessDialogForm.platform) return;

      try {
        const res = await this.$axios.get(`/api/operation/media/video/${this.businessDialogForm.id}`, {
          params: { business: this.businessDialogForm.business, platform: this.businessDialogForm.platform }
        });
        if (res.data && res.data.code === 0 && res.data.data && res.data.data.conflict_video) {
          this.businessConflictVideo = res.data.data.conflict_video;
        } else {
          this.businessConflictVideo = null;
        }
      } catch (e) {
        this.businessConflictVideo = null;
      }
    },
    async handleBusinessSave() {
      if (!this.businessDialogForm.business && !this.businessDialogForm.platform) {
        this.businessDialogVisible = false;
        return;
      }
      this.businessDialogLoading = true;
      try {
        const res = await this.$axios.put(`/api/operation/media/video/${this.businessDialogForm.id}`, {
          business: this.businessDialogForm.business,
          platform: this.businessDialogForm.platform,
        });
        if (res.data && res.data.code === 0) {
          this.$message.success('平台或展示位置已更新');
          this.businessDialogVisible = false;
          this.get_data();
        } else {
          this.$message.error(res.data?.message || '更新失败');
        }
      } catch (e) {
        this.$message.error('更新失败');
      }
      this.businessDialogLoading = false;
    },
    subtitleNames(row, maxLen = 20) {
      if (!row.subtitles) return '';
      const names = Object.values(row.subtitles).map(s => s.name).join(' ');
      if (maxLen && names.length > maxLen) {
        return names.slice(0, maxLen) + '...';
      }
      return names;
    },
    getSubtitleDisplay(row, max = 4) {
      if (!row.subtitles) {
        return { cellContent: '', tooltipContent: '', showTooltip: false };
      }
      const list = Object.values(row.subtitles).map(s => ({
        name: s.name,
        file_url: s.file_url,
        is_translated: s.is_translated
      }));
      // 单元格内容：只展示前 max 个
      const cellArr = list.slice(0, max).map(item => {
        const style = item.is_translated ? 'color: #67C23A;' : '';
        const label = item.name;
        return `<a href="${item.file_url}" target="_blank" style="${style}">${label}</a>`;
      });
      let cellContent = cellArr.join(', ');
      if (list.length > max) cellContent += ' ... ';
      cellContent += `（${list.length}）`;

      // tooltip 内容：全部展示
      const tooltipArr = list.map(item => {
        const style = item.is_translated ? 'color: #67C23A;' : '';
        const label = item.name;
        return `<a href="${item.file_url}" target="_blank" style="${style}">${label}</a>`;
      });
      const tooltipContent = tooltipArr.join('<br/>');

      return {
        cellContent,
        tooltipContent,
        showTooltip: list.length > max
      };
    },
    copyLink(url) {
      if (navigator && navigator.clipboard) {
        navigator.clipboard.writeText(url).then(() => {
          this.$message.success('已复制');
        }, () => {
          this.$message.error('复制失败');
        });
      } else {
        // 兼容性降级
        const input = document.createElement('input');
        input.value = url;
        document.body.appendChild(input);
        input.select();
        try {
          document.execCommand('copy');
          this.$message.success('已复制');
        } catch (e) {
          this.$message.error('复制失败');
        }
        document.body.removeChild(input);
      }
    }
  },
  async mounted() {
    await this.get_enums();
    await this.get_data();
  },
};
</script>
