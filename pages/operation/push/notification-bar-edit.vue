<template>
  <div class="table-data">
    <el-row type="flex" justify="space-between" align="middle">
      <el-col :span="22">
        <h2 style="font-family: 'Microsoft YaHei', Arial, sans-serif">
          通知栏-编辑
        </h2>
      </el-col>

      <el-col :span="1">
        <el-tooltip
          content="刷新"
          placement="right"
          :open-delay="500"
          :hide-after="2000"
        >
          <el-button
            icon="el-icon-refresh-left"
            circle
            @click="get_data"
          ></el-button>
        </el-tooltip>
      </el-col>
    </el-row>
    <el-collapse v-model="activeNames">
      <el-collapse-item name="0">
        <template slot="title"
          ><h3 style="font-family: 'Microsoft YaHei', Arial, sans-serif">
            基本信息
          </h3></template
        >
        <el-form :model="filters_form" label-width="100px" v-loading="loading">
          <el-form-item label="通知名称: " required>
            <el-input v-model="filters_form.name" style="width: 300px">
            </el-input>
          </el-form-item>

          <el-form-item label="通知终端: " required>
            <el-radio-group
              v-model="filters_form.platform"
              @change="changeSelectableTriggerPages"
            >
              <template v-for="(name, key) in platforms">
                <el-radio :label="key" :key="key">{{
                  name.toUpperCase()
                }}</el-radio>
              </template>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="通知位置: " required>
            <el-tooltip
              content="添加"
              placement="right"
              :open-delay="500"
              :hide-after="2000"
            >
              <el-button icon="el-icon-circle-plus" circle @click="addItem">
              </el-button>
            </el-tooltip>
            <div
              v-for="(item, index) in filters_form.trigger_pages"
              :key="index"
            >
              <el-select
                v-model="item.trigger_page"
                style="width: 300px"
                @change="initTriggerPageParams(item)"
              >
                <el-option
                  v-for="(label, value) in selectable_trigger_pages"
                  :key="value"
                  :label="label"
                  :value="value"
                >
                </el-option>
              </el-select>

              <el-button @click="deleteItem(item, index)" type="danger"
                >删除</el-button
              >
            </div>
          </el-form-item>

          <el-form-item label="开始时间:" required>
            <el-date-picker
              v-model="filters_form.begin_at"
              type="datetime"
              clearable
              style="width: 300px"
            >
            </el-date-picker>
            <el-row>
              <span>UTC时间：{{ $formatUTCDate(filters_form.begin_at) }}</span>
            </el-row>
          </el-form-item>

          <el-form-item label="结束时间:" required>
            <el-date-picker
              v-model="filters_form.end_at"
              type="datetime"
              clearable
              style="width: 300px"
            >
            </el-date-picker>
            <el-row>
              <span>UTC时间：{{ $formatUTCDate(filters_form.end_at) }}</span>
            </el-row>
          </el-form-item>

          <el-form-item label="是否跳转: " required>
            <el-radio-group v-model="filters_form.jump_page_enabled">
              <el-radio :label="false">否</el-radio>
              <el-radio :label="true">是</el-radio>
            </el-radio-group>
          </el-form-item>

          <span v-if="filters_form.jump_page_enabled">
            <el-form-item label="跳转类型: " required>
              <el-select
                v-model="filters_form.jump_type"
                style="width: 300px"
                @change="getTargetPages()"
              >
                <el-option
                  v-for="(label, value) in selectable_jump_types"
                  :key="value"
                  :label="label"
                  :value="value"
                >
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="目标页: " required>
              <el-select
                v-model="filters_form.jump_id"
                style="width: 300px"
                filterable
              >
                <el-option
                  v-for="target_page in target_pages"
                  :key="target_page.id"
                  :label="`${target_page.remark} -- ${target_page.jump_data}`"
                  :value="target_page.id"
                >
                </el-option>
              </el-select>
              <el-button
                icon="el-icon-s-primary"
                type="primary"
                @click="handleJumpPages"
                >跳转管理</el-button
              >
            </el-form-item>
          </span>

          <el-form-item label="备注:">
            <el-input v-model="filters_form.remark" style="width: 300px">
            </el-input>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="save_to_draft()">
              保存规则
            </el-button>
          </el-form-item>
        </el-form>
      </el-collapse-item>
      <template v-if="$route.query.id !== '0' && contents_visible">
        <el-collapse-item name="1">
          <template slot="title">
            <h3 style="font-family: 'Microsoft YaHei', Arial, sans-serif">
              通知内容
            </h3>
            &#12288;
          </template>
          <MessageEditor
            :messageConfig="messageConfig"
            :contents="contents"
            :languages="languages"
            :template_filters="template_filters"
            :disabled="false"
            @afterSave="afterSaveDisplay"
          ></MessageEditor>
        </el-collapse-item>
      </template>
    </el-collapse>

    <el-dialog title="跳转管理" :visible.sync="jump_show" width="70%">
      <el-tooltip
        content="新建"
        placement="right"
        :open-delay="500"
        :hide-after="2000"
      >
        <el-button
          type="primary"
          icon="el-icon-plus"
          circle
          @click="handleJumpCreate"
        ></el-button>
      </el-tooltip>
      <el-table :data="jump_pages" stripe>
        <el-table-column prop="remark" label="跳转标注"> </el-table-column>

        <el-table-column prop="jump_data" label="跳转链接"> </el-table-column>

        <el-table-column
          prop="jump_type"
          :formatter="(row) => jump_types[row.jump_type]"
          label="跳转类型"
        >
        </el-table-column>

        <el-table-column prop="operation" label="操作">
          <template slot-scope="scope">
            <el-tooltip
              content="编辑"
              placement="right"
              :open-delay="500"
              :hide-after="2000"
            >
              <el-button
                size="mini"
                type="primary"
                icon="el-icon-edit"
                circle
                @click="handleJumpEdit(scope.row)"
              ></el-button>
            </el-tooltip>
            <el-tooltip
              content="删除"
              placement="right"
              :open-delay="500"
              :hide-after="2000"
            >
              <el-button
                size="mini"
                type="danger"
                icon="el-icon-delete"
                circle
                @click="handleDeleteJump(scope.row)"
              ></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <el-dialog
      :title="jump_action === DIALOG_CREATION ? '添加跳转' : '编辑跳转'"
      :visible.sync="jump_action_show"
      :before-close="handleClose"
      width="60%"
    >
      <el-form
        :model="jump_data"
        ref="jump_data"
        label-width="80px"
        :validate-on-rule-change="false"
      >
        <el-form-item label="跳转标示">
          <el-input v-model="jump_data.remark"></el-input>
        </el-form-item>

        <el-form-item label="跳转类型" required>
          <el-select clearable v-model="jump_data.jump_type">
            <el-option
              v-for="(value, key) in jump_types"
              :key="value"
              :label="value"
              :value="key"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="跳转链接" required>
          <el-input v-model="jump_data.jump_data"></el-input>
        </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="jump_submit">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import Vue from "vue";
import moment from "moment";
import { Chart } from "highcharts-vue";
import VueClipboard from "vue-clipboard2";
import MessageEditor from "@/components/MessageEditor";

Vue.use(VueClipboard);

export default {
  components: {
    MessageEditor,
    highcharts: Chart,
  },
  methods: {
    get_data() {
      let id = this.$route.query.id;
      if (id === "0") {
        this.activeNames = ["0", "1", "4"];
      }
      this.loading = true;
      this.$axios.get(`/api/operation/notification-bar/${id}`).then((res) => {
        this.loading = false;
        if (res?.data?.code === 0) {
          let data = res.data.data;
          this.assign_form(data);
          this.fetchJumpPages();
          this.get_contents();
          this.updateSelectableTriggerPages();
        } else {
          this.$message.error(
            `code: ${res.data?.code}; message: ${res.data?.message}`
          );
        }
      });
    },
    assign_form(data) {
      // debugger;
      let form = this.filters_form;
      form.name = data.name;
      form.remark = data.remark;
      form.platform = data.platform;
      form.trigger_pages = data.trigger_pages || [];
      form.jump_type = data.jump_type || null;
      form.jump_id = data.jump_id || null;
      form.begin_at = data.begin_at ? new Date(data.begin_at * 1000) : null;
      form.end_at = data.end_at ? new Date(data.end_at * 1000) : null;
      form.jump_page_enabled = data.jump_page_enabled;
      this.cur_status = data.status;
      let extra = data.extra;
      this.platforms = extra.platforms;
      this.trigger_pages = extra.trigger_pages;
      this.selectable_trigger_pages = { ...this.trigger_pages };
      this.jump_types = extra.jump_types;
      this.selectable_jump_types = { ...this.jump_types };
      this.assets = extra.assets;
      this.spot_markets = extra.spot_markets;
      this.perpetual_markets = extra.perpetual_markets;
      this.languages = extra.languages;
      let lang_list = Object.keys(this.languages);
      if (!lang_list.includes(this.cur_lang)) {
        this.messageConfig.cur_lang = lang_list[0];
      }
      this.contents = Object.fromEntries(
        lang_list.map((lang) => [
          lang,
          this.contents[lang] || {
            title: "",
            content: "",
            summary: "",
          },
        ])
      );
    },

    fetchJumpPages() {
      let url = "/api/operation/page/new-app-entrances/jump-list";
      let jump_pages = this.jump_pages;
      this.$axios.get(url).then((res) => {
        if (res?.data?.code === 0) {
          jump_pages = res.data.data;
          jump_pages.forEach((e) => {
            if (e.jump_type === "原生") {
              e.jump_type = "NATIVE";
            }
          });
          this.jump_pages = jump_pages;
          this.getTargetPages(true);
        } else {
          this.$message.error(
            `code: ${res.data?.code}; message: ${res.data?.message}`
          );
        }
      });
    },
    getTargetPages(init = false) {
      // debugger;
      if (!init) {
        this.filters_form.jump_id = null;
      }
      let jumpType = this.filters_form.jump_type;
      let target_pages = [];
      this.jump_pages.forEach((e) => {
        if (e.jump_type === jumpType) {
          target_pages.push({
            id: e.id,
            remark: e.remark,
            jump_data: e.jump_data,
          });
        }
      });
      this.target_pages = target_pages;
    },

    handleJumpPages() {
      this.jump_show = true;
    },
    handleJumpCreate() {
      this.jump_action = this.DIALOG_CREATION;
      this.jump_data = {};
      this.jump_action_show = true;
    },
    handleJumpEdit(row) {
      this.jump_action = this.DIALOG_EDIT;
      this.jump_data = _.clone(row);
      this.jump_action_show = true;
    },
    handleDeleteJump(row) {
      this.$confirm(`确认删除跳转 ${row.id} ${row.remark}?`).then(() => {
        this.$axios
          .delete(`/api/operation/page/new-app-entrances/jump-list/${row.id}`)
          .then((res) => {
            this.fetchJumpPages();
            if (res.data.code === 0) {
              this.dialog_show = false;
              this.jump_show = false;
              this.jump_action_show = false;
              this.jump_action = "";
              this.$message.success("删除成功!");
              this.loading = false;
            } else {
              this.$message.error(
                `code: ${res.data?.code}; message: ${res.data?.message}`
              );
            }
          })
          .catch((err) => {
            this.$message.error(`失败! (${err})`);
          });
      });
    },

    addItem() {
      this.filters_form.trigger_pages.push({
        trigger_page: null,
        trigger_page_params: [],
      });
    },

    deleteItem(item, index) {
      this.filters_form.trigger_pages.splice(index, 1);
    },

    jump_submit() {
      this.$refs["jump_data"].validate((valid) => {
        if (!valid) {
          this.$alert("校验失败请修改", "校验失败请修改", {
            confirmButtonText: "确定",
          });
        } else {
          let method = "put";
          if (this.jump_action === this.DIALOG_CREATION) {
            method = "post";
          }
          this.$axios[method](
            "/api/operation/page/new-app-entrances/jump-list",
            this.jump_data
          )
            .then((res) => {
              this.fetchJumpPages();
              if (res.data.code === 0) {
                this.res_success_notice(res);
                this.dialog_show = false;
                this.jump_show = false;
                this.jump_action_show = false;
                this.jump_action = "";
              } else {
                this.res_fail_notice(res);
              }
            })
            .catch((_) => {
              this.res_error_notice(res);
            });
        }
      });
    },

    notice(title, message) {
      this.$alert(message, title, {
        confirmButtonText: "确定",
      });
    },
    res_success_notice(r) {
      let title = "提交成功";
      this.notice(title, title);
    },
    res_fail_notice(res) {
      let title = "提交失败";
      let message = `(code: ${res.data?.code}; message: ${res.data?.message})`;
      this.notice(title, message);
    },
    res_error_notice(error) {
      let title = "提交失败";
      let message = `(code: ${error.response.status}; message: ${error.message})`;
      this.notice(title, message);
    },

    format_date(timestamp, pattern = "YYYY-MM-DD HH:mm:ss") {
      return moment(Number(timestamp) * 1000).format(pattern);
    },
    async get_contents(id = 0) {
      if (!id) {
        id = this.$route.query.id;
      }
      if (id === "0") {
        return;
      }
      let lang_list = Object.keys(this.languages);

      let contents = {};
      const maxConcurrency = 5;
      let running = 0;
      let queue = [...lang_list];
      this.contents_visible = false; // Initialize to false

      const processQueue = async () => {
        while (running < maxConcurrency && queue.length > 0) {
          const lang = queue.shift();
          running++;

          try {
            const res = await this.$axios.get(`/api/operation/notification-bar/${id}/langs/${lang}`);

            if (res?.data?.code === 0) {
              let data = res.data.data;
              contents[lang] = {
                title: data.title,
                content: data.content,
                summary: data.summary || "",
              };
            } else {
              console.error(`语言 ${lang} 内容获取失败:`, res);
            }
          } catch (error) {
            console.error(`语言 ${lang} 内容获取失败:`, error);
          } finally {
            running--;
            if (queue.length === 0 && running === 0) {
              this.contents_visible = lang_list.every((lang) => contents[lang]);
            }
            processQueue(); // Process next item in the queue
          }
        }
      };
      processQueue();
      this.contents = contents;
    },
    get_contents1(id = 0) {
      if (!id) {
        id = this.$route.query.id;
      }
      if (id === "0") {
        return;
      }
      let contents = this.contents;
      let lang_list = Object.keys(this.languages);
      lang_list.forEach((lang) => {
        this.$axios
          .get(`/api/operation/notification-bar/${id}/langs/${lang}`)
          .then((res) => {
            if (res?.data?.code === 0) {
              let data = res.data.data;
              contents[lang] = {
                title: data.title,
                content: data.content,
                summary: data.summary || "",
              };
              this.contents_visible = lang_list.every((lang) => contents[lang]);
            }
          });
      });
    },
    save_to_draft() {
      this.save_form("DRAFT");
    },
    async save_form(status) {
      // debugger;
      let form = this.filters_form;
      form.status = status;
      if (!form.name) {
        this.$message.error("请输入名称!");
        return;
      }
      if (!form.platform) {
        this.$message.error("请输入通知终端!");
        return;
      }
      if (form.trigger_pages.length <= 0) {
        this.$message.error("请输入通知位置!");
        return;
      }
      let paramPages = {};
      let pages = [];
      let checkSuccess = true;
      form.trigger_pages.forEach((e) => {
        if (!e.trigger_page) {
          checkSuccess = false;
        }
        if (
          ["ASSET_DATA", "SPOT_MARKET", "PERPETUAL_MARKET"].includes(
            e.trigger_page
          )
        ) {
          if (paramPages.hasOwnProperty(e.trigger_page)) {
            if (!e.trigger_page_params || e.trigger_page_params.length === 0) {
              paramPages[e.trigger_page].push(null);
            } else {
              paramPages[e.trigger_page].push(...e.trigger_page_params);
            }
          } else {
            if (!e.trigger_page_params || e.trigger_page_params.length === 0) {
              paramPages[e.trigger_page] = [null];
            } else {
              paramPages[e.trigger_page] = [...e.trigger_page_params];
            }
          }
        } else {
          pages.push(e.trigger_page);
        }
      });
      if (!checkSuccess) {
        this.$message.error("请输入通知位置!");
        return;
      }
      if (pages.length != new Set(pages).size) {
        this.$message.error("通知位置不能重复!");
        return;
      }
      let paramPageCheckSuccess = true;
      Object.keys(paramPages).map((key) => {
        if (paramPages[key].length != new Set(paramPages[key]).size) {
          paramPageCheckSuccess = false;
        }
      });
      if (!paramPageCheckSuccess) {
        this.$message.error("相同通知位置，触发条件不能重复!");
        return;
      }
      if (!form.begin_at) {
        this.$message.error("请输入开始时间!");
        return;
      }
      if (!form.end_at) {
        this.$message.error("请输入结束时间!");
        return;
      }
      if (!form.jump_page_enabled) {
        form.jump_page_enabled = false;
        delete form.jump_id;
        delete form.jump_type;
      } else {
        if (!form.jump_type) {
          this.$message.error("请输入跳转类型!");
          return;
        }
        if (!form.jump_id) {
          this.$message.error("请输入跳转目标页!");
          return;
        }
      }

      if (!(await this.canPostReq())) {
        return;
      }
      // debugger;
      let id = this.$route.query.id;
      if (id === "0") {
        this.loading = true;
        this.$axios
          .post(`/api/operation/notification-bars`, form)
          .then((res) => {
            this.loading = false;
            if (res?.data?.code === 0) {
              let data = res.data.data;
              let id = data.id;
              this.$router.replace({ query: { id: id } });
              this.$message.success("保存成功!");
              this.get_contents(id);
            } else {
              this.$message.error(
                `保存失败! (code: ${res.data?.code}; message: ${res.data?.message})`
              );
            }
          })
          .catch((err) => {
            this.loading = false;
            this.$message.error(`提交失败! (${err})`);
          });
      } else {
        if (!this.curStatusPermitted(true)) {
          this.$message.error("当前状态不允许修改!");
          return;
        }
        this.loading = true;
        this.$axios
          .put(`/api/operation/notification-bar/${id}`, form)
          .then((res) => {
            this.loading = false;
            if (res?.data?.code === 0) {
              let data = res.data.data;
              this.cur_status = data.status;
              this.$message.success("保存成功!");
            } else {
              this.$message.error(
                `保存失败! (code: ${res.data?.code}; message: ${res.data?.message})`
              );
            }
          })
          .catch((err) => {
            this.loading = false;
            this.$message.error(`保存失败! (${err})`);
          });
      }
      this.afterSaveDisplay();
    },

    async canPostReq() {
      let id = this.$route.query.id;
      let pages = JSON.stringify(this.filters_form.trigger_pages);
      let res = await this.$axios.get(
        `/api/operation/notification-bar/duplicates`,
        { params: { ...this.filters_form, id: id, trigger_pages: pages } }
      );
      if (res?.data?.code === 0) {
        let data = res.data.data;
        let success = true;
        for (let page in data) {
          if (data[page] >= 10) {
            this.$message.error(
              `${this.trigger_pages[page]} 已存在至少 ${data[page]} 个通知，不可继续新增！`
            );
            success = false;
          }
        }
        return success;
      } else {
        return false;
      }
    },
    curStatusPermitted(fromRuleSave = false) {
      // debugger;
      let permitStatusList = ["DRAFT", "CREATED", "FAILED"];
      if (fromRuleSave) {
        permitStatusList = [
          "DRAFT",
          "CREATED",
          "AUDITED",
          "REJECTED",
          "FAILED",
        ];
      }
      return !this.cur_status || permitStatusList.includes(this.cur_status);
    },
    afterSaveDisplay() {
      this.activeNames = ["0", "1", "2"];
    },
    handle_template() {
      this.dialog_template = true;
      this.template_filters.title = "";
      this.template_query();
    },
    handle_template_change() {
      this.template_filters.title = null;
      this.template_query();
    },
    template_query() {
      this.template_loading = true;
      this.$axios
        .get("/api/operation/templates", { params: this.template_filters })
        .then((res) => {
          this.template_loading = false;
          if (res && res.data.code === 0) {
            let data = res.data.data;
            this.template_items = data.items;
            this.template_total = data.total;
          } else {
            this.items = [];
            this.$message.error(
              `code: ${res.data?.code}; message: ${res.data?.message}`
            );
          }
        });
    },
    handleCurrentChange(row, column, cell, event) {
      if (column.label === "操作") {
        return;
      }
      this.$confirm(`确认选择?`).then(() => {
        let id = row.id;
        if (!id) {
          return;
        }
        let contents = this.contents;
        let lang_list = Object.keys(this.languages);
        lang_list.forEach((lang) => {
          this.$axios
            .get(`/api/operation/template-content/${id}/langs/${lang}`)
            .then((res) => {
              if (res?.data?.code === 0) {
                // debugger;
                let data = res.data.data;
                contents[lang] = {
                  title: data.title,
                  content: data.content,
                  summary: data.summary,
                };
              }
            });
        });
        this.dialog_template = false;
      });
    },
    handleClose(done) {
      this.$confirm("确认关闭？")
        .then((_) => {
          this.dialog_push_test = false;
          this.dialog_template = false;
          done();
        })
        .catch((_) => {});
    },

    changeSelectableTriggerPages() {
      this.filters_form.jump_type = null;
      this.filters_form.trigger_pages = [];
      this.selectable_trigger_pages = { ...this.trigger_pages };
      this.selectable_jump_types = { ...this.jump_types };
      this.updateSelectableTriggerPages();
    },

    updateSelectableTriggerPages() {
      if (this.filters_form.platform === "APP") {
        delete this.selectable_trigger_pages["FIAT"];
      } else if (this.filters_form.platform === "ALL") {
        delete this.selectable_trigger_pages["FIAT"];
        delete this.selectable_trigger_pages["ACCOUNT_ASSET"];

        delete this.selectable_jump_types["NATIVE"];
      } else if (this.filters_form.platform === "WEB") {
        delete this.selectable_jump_types["NATIVE"];
      }
    },

    initTriggerPageParams(item) {
      // debugger
      item.trigger_page_params = null;
    },
  },
  mounted() {
    this.get_data();
  },
  provide() {
    return { testSendFunc: null };
  },
  data() {
    return {
      messageConfig: {
        extr_params: { summary: "摘要" },
        has_title: true,
        use_editor: true,
        save_url: "/api/operation/notification-bar/${id}/langs/${lang}",
        cur_lang: null,
        has_test: false,
      },
      filters_form: {
        name: "",
        platform: null,
        trigger_pages: [],
        jump_type: null,
        jump_id: null,
        begin_at: null,
        end_at: null,
        jump_page_enabled: false,
        remark: null,
      },

      jump_action: null,
      jump_show: false,
      jump_action_show: false,
      jump_data: {},
      DIALOG_CREATION: "CREATION",
      DIALOG_EDIT: "EDIT",

      assets: [],
      spot_markets: [],
      perpetual_markets: [],
      trigger_pages: {},
      selectable_trigger_pages: {},
      platforms: {},
      jump_types: {},
      selectable_jump_types: {},
      jump_pages: [],
      target_pages: [],
      languages: {},
      cur_lang: null,
      cur_status: null,
      contents: {},
      contents_visible: false,
      dialog_template: false,
      template_items: [],
      template_filters: {
        business: "NOTIFICATION_BAR",
        enabled: true,
        title: null,
        page: null,
        limit: 10,
      },
      template_total: 0,
      template_loading: false,
      activeNames: ["0"],
      audit_data: [],
      loading: false,
      url_loading: false,
    };
  },
};
</script>
