<template>
  <div class="table-data">
    <h2 style="font-family: 'Microsoft YaHei',Arial,sans-serif;">
      链上资产负债
    </h2>

    <el-form :inline="true" :model="search_data">
      <el-form-item label="公链">
        <el-select v-model="search_data.chain" clearable placeholder="<ALL>" style="width: 120px;"
                   @change="search(true)">
          <el-option v-for="v in chains" :key="v" :label="v" :value="v">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="合约地址">
        <el-input v-model="search_data.contract" placeholder="请输入" style="width: 600px;" @change="search(true)"/>
      </el-form-item>
      <el-form-item label="时间" v-show="search_data.chain">
        <el-col :span="11">
          <el-date-picker
            v-model="search_data.start_time"
            type="datetime"
            value-format="timestamp"
            placeholder="开始时间"
            @change="search(true)">
          </el-date-picker>
        </el-col>
        <el-col class="line" :span="2">-</el-col>
        <el-col :span="11">
          <el-date-picker
            v-model="search_data.end_time"
            type="datetime"
            value-format="timestamp"
            placeholder="结束时间"
            @change="search(true)">
          </el-date-picker>
        </el-col>
      </el-form-item>
      <el-form-item label="上次不平USD阈值" v-show="!search_data.chain">
        <el-input v-model="search_data.min_usd" clearable style="width: 120px;"/>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-refresh-left" circle @click="search(true)"/>
      </el-form-item>
    </el-form>

    <el-table :data="items"
              :cell-style="handleCellStyle"
              v-loading="loading"
              height="1250"
              stripe
              v-if="!loading">
      <el-table-column prop="symbol" label="symbol" width="100px"></el-table-column>
      <el-table-column prop="name" label="name" width="200px"></el-table-column>
      <el-table-column prop="chain" label="公链" width="100px"></el-table-column>
      <el-table-column prop="contract" label="合约地址"></el-table-column>

      <el-table-column label="链上总资产" prop="wallet_total">

        <el-table-column
          prop="wallet_balance"
          :formatter="row => formatAssetAmount(row.wallet_balance)"
          label="钱包-代持Token数量">
        </el-table-column>
      </el-table-column>

      <el-table-column label="用户持仓" prop="user_total">
        <el-table-column
          prop="user_balance"
          :formatter="row => formatAssetAmount(row.user_balance)"
          label="代持Token数量">
        </el-table-column>
      </el-table-column>

      <el-table-column prop="diff" label="对账差异"></el-table-column>

      <el-table-column prop="diff_usd" label="权益市值"></el-table-column>

      <el-table-column
        label="更新时间"
        :formatter="row => formatDate(row.created_at)">
      </el-table-column>

    </el-table>
    <el-pagination :current-page.sync="search_data.page"
                   :page-size.sync="search_data.limit"
                   @size-change="search(false)"
                   @current-change="search(false)"
                   :page-sizes="[100, 50]"
                   :hide-on-single-page="true"
                   layout="total, sizes, prev, pager, next, jumper"
                   :total="total"
                   v-show="search_data.chain">
    </el-pagination>
    <el-backtop/>

  </div>
</template>

<script>

import moment from "moment";

export default {
  mounted() {
    this.get_data();
  },
  methods: {
    formatDate(timestamp, pattern = 'YYYY-MM-DD HH:mm:ss') {
      return timestamp ? moment(Number(timestamp) * 1000).format(pattern) : null;
    },
    get_data() {
      let query = this.search_data;
      this.loading = true;
      this.$axios.get(`/api/onchain/statistic/asset-liability`, {params: query})
        .then((res) => {
          if (res.data.code === 0) {
            let data = res.data.data;
            this.items = data.items;
            this.chains = data.chains;
            this.total = data.total;
            this.loading = false;
          } else {
            this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
          }
        })
        .catch((e) => {
          this.$message.error(`刷新失败! (${e})`);
        })
    },
    search(reset = true) {
      if (this.search_data.asset) {
        this.search_data.min_usd = null;
      }
      if (reset === true) {
        this.search_data.page = 1;
      }
      this.get_data();
    },
    handleCellStyle({row, column, rowIndex, columnIndex}) {
      let green_columns = [
        'wallet_balance',
      ];
      let yellow_columns = [
        'user_balance',
      ];

      if (green_columns.indexOf(column.property) !== -1) {
        return "background: LimeGreen";
      }
      if (yellow_columns.indexOf(column.property) !== -1) {
        return "background: Yellow";
      }
    },
    toNonExponential(num) {
      const number = Number(num);
      const m = number.toExponential().match(/\d(?:\.(\d*))?e([+-]\d+)/);
      return number.toFixed(Math.max(0, (m[1] || '').length - m[2]));
    },
    formatAssetAmount(amount) {
      // 10位有效数字，按千分位逗号隔离
      const b = this.toNonExponential(Number(amount).toPrecision(10));
      if (Math.abs(b) > 1000000) {
        return Number(b).toLocaleString()
      }
      return b
    },
  },
  data() {
    return {
      search_data: {
        chain: null,
        contract: null,
        min_usd: null,
        start_time: null,
        end_time: null,
        page: 1,
        limit: 100
      },
      loading: false,
      items: [],
      chains: [],
      total: 0
    }
  }
}

</script>

<style>
.el-table .warning {
  color: blue;
}

.el-table .danger {
  color: orange;
}

.el-table .error {
  color: red;
}
</style>
