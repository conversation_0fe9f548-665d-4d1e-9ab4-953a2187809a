<template>
  <el-container>
    <el-main>
      <h2 style="font-family: 'Microsoft YaHei',Arial,sans-serif;">
        链上交易报表-全站
      </h2>

      <el-tabs v-model="filters.report_type" type="card" @tab-click="handle_page_refresh()">
        <el-tab-pane label="日报" name="DAILY"></el-tab-pane>
        <el-tab-pane label="月报" name="MONTHLY"></el-tab-pane>
      </el-tabs>

      <el-form :inline="true" :model="filters">
        <el-form-item prop="start_date" label="开始时间">
          <el-date-picker
            v-model="filters.start_date"
            :type="filters.report_type == 'DAILY' ? 'date' : 'month'"
            value-format="yyyy-MM-dd"
            :picker-options="pickerOptions"
            placeholder="时间"
            @change="handle_page_refresh"
          >
          </el-date-picker>
        </el-form-item>

        <el-form-item prop="end_date" label="结束时间">
          <el-date-picker
            v-model="filters.end_date"
            :type="filters.report_type == 'DAILY' ? 'date' : 'month'"
            value-format="yyyy-MM-dd"
            :picker-options="pickerOptions"
            placeholder="时间"
            @change="handle_page_refresh"
          >
          </el-date-picker>
        </el-form-item>

        <el-form-item>
          <el-tooltip content="导出数据" placement="right" :open-delay="500" :hide-after="2000">
            <el-button icon="el-icon-download" circle type="primary" @click="download"></el-button>
          </el-tooltip>
        </el-form-item>

        <el-form-item>
          <el-tooltip content="刷新" placement="right" :open-delay="500" :hide-after="2000">
            <el-button icon="el-icon-refresh-left" circle @click="handle_page_refresh"></el-button>
          </el-tooltip>
        </el-form-item>
      </el-form>

      <FixedTable :data="items" style="width: 100%" @header-click="show_series_dialog" ref="table">
        <el-table-column label="日期" prop="report_date" min-width="150">
          <template slot-scope="scope">
            <el-link
              :href="`/report/onchain-trade-token-report?report_type=${filters.report_type}`"
              type="primary"
              target="_blank"
              :underline="false">
              {{ $formatDate(scope.row.report_date, filters.report_type === 'DAILY' ? 'YYYY-MM-DD' : 'YYYY-MM') }}
            </el-link>
          </template>
        </el-table-column>

        <el-table-column
          prop="trade_usd"
          :formatter="row => $formatNumber(row.trade_usd, 2)"
          label="全站成交额(USD)"
          :render-header="renderHeader"
          column-key="全站成交额(USD)：全站成交额(USD)"
          min-width="180"
        >
        </el-table-column>

        <el-table-column
          prop="fee_usd"
          :formatter="row => $formatNumber(row.fee_usd, 2)"
          label="手续费收入(USD)"
          :render-header="renderHeader"
          column-key="手续费收入(USD)：手续费收入(USD)"
          min-width="180"
        >
        </el-table-column>

        <el-table-column
          prop="gas_usd"
          :formatter="row => $formatNumber(row.gas_usd, 2)"
          label="gas费(USD)"
          :render-header="renderHeader"
          column-key="gas费(USD)：gas费(USD)"
          min-width="150"
        >
        </el-table-column>

        <el-table-column
          prop="trade_token_count"
          label="成交token数"
          :render-header="renderHeader"
          column-key="成交token数：成交token数"
          min-width="140"
        >
        </el-table-column>

        <el-table-column
          prop="trade_user_count"
          label="成交人数"
          :render-header="renderHeader"
          column-key="成交人数：成交人数"
          min-width="120"
        >
        </el-table-column>

        <el-table-column
          prop="avg_trade_usd_per_user"
          :formatter="row => $formatNumber(row.avg_trade_usd_per_user, 2)"
          label="人均交易市值(USD)"
          :render-header="renderHeader"
          column-key="人均交易市值(USD)：人均交易市值(USD)"
          min-width="180"
        >
        </el-table-column>

        <el-table-column
          prop="avg_token_count_per_user"
          :formatter="row => $formatNumber(row.avg_token_count_per_user, 2)"
          label="人均委托token数"
          :render-header="renderHeader"
          column-key="人均委托token数：人均委托token数"
          min-width="160"
        >
        </el-table-column>
      </FixedTable>

      <el-pagination
        :current-page.sync="filters.page"
        :page-size.sync="filters.limit"
        :page-sizes="[50, 100, 200, 500]"
        @current-change="handle_page_change"
        :hide-on-single-page="false"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>

      <el-dialog :visible.sync="show_dialog" width="80%" :before-close="handleClose">
        <Series ref="series" :render_info="render_info" v-if="show_dialog === true" @getChildValue="setChildValue"></Series>
      </el-dialog>

      <el-backtop></el-backtop>
    </el-main>
  </el-container>
</template>

<script>
import Vue from 'vue';
import VueClipboard from 'vue-clipboard2';
import Series from '~/components/Series';
import FixedTable from '~/components/FixedTableRender.vue';
import series from '~/plugins/report/series';
Vue.use(VueClipboard);

const binding_url = '/api/report/onchain/site';
const exclude_render_columns = ['report_date'];
export default {
  components: {
    Series,
    FixedTable,
  },
  mixins: [series],
  methods: {
    show_series_dialog(column) {
      if (exclude_render_columns.includes(column.property)) {
        return;
      }
      this.show_dialog = true;
      this.set_render_info(column, {
        exclude_columns: exclude_render_columns,
        binding_url: binding_url,
        resp_key: 'items',
      });
    },
    get_data() {
      this.loading = true;
      this.$axios.get(binding_url, { params: this.filters }).then(res => {
        this.loading = false;
        if (res && res.data.code === 0) {
          let data = res.data.data;
          this.items = data.items;
          this.total = data.total;
        } else {
          this.items = [];
          this.total = 0;
          this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
        }
      });
    },
    handle_page_change() {
      this.get_data();
    },
    handle_page_refresh() {
      this.reset_page();
      this.get_data();
    },
    reset_page() {
      this.filters.page = 1;
    },
    download() {
      let filename;
      if (this.filters.report_type && this.filters.report_type.toLowerCase() === 'monthly') {
        filename = 'onchain_trade_site_monthly.xlsx';
      } else {
        filename = 'onchain_trade_site_daily.xlsx';
      }
      let params = { ...this.filters, export: true };
      this.$download_from_url(binding_url, filename, params);
    },
    update_router_query() {
      let query = {};
      Object.assign(query, this.filters);
      Object.keys(query).forEach(key => !query[key] && delete query[key]);
      this.$router.replace({ query: query });
    },
  },
  created() {
    let query = this.$route.query;
    let filters = this.filters;
    filters.page = query.page ? parseInt(query.page) : 1;
    filters.report_type = query.report_type || 'DAILY';
    this.$watch('filters', {
      handler: function() {
        this.update_router_query();
      },
      deep: true,
    });
  },
  mounted() {
    this.get_data();
  },
  data() {
    return {
      filters: {
        page: 1,
        limit: 50,
        report_type: 'DAILY',
        start_date: null,
        end_date: null,
      },
      items: [],
      total: 0,
      loading: true,
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
    };
  },
};
</script>
